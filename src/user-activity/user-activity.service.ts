import { Injectable, HttpStatus } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { ActivityAction, ActivityObject, UserActivity } from '@prisma/client';
import { BaseResponse } from 'src/common/dto/base-response.dto';

interface LogActivityDto {
  userId: string;
  action: ActivityAction;
  objectType: ActivityObject;
  objectId: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class UserActivityService {
  constructor(private readonly prisma: PrismaService) {}

  async logActivity(
    data: LogActivityDto,
  ): Promise<BaseResponse<UserActivity | null>> {
    try {
      const result = await this.prisma.userActivity.create({
        data: {
          userId: data.userId,
          action: data.action,
          objectType: data.objectType,
          objectId: data.objectId,
          metadata: data.metadata,
        },
      });

      return new BaseResponse({
        data: result,
        message: 'Activity logged successfully.',
        statusCode: HttpStatus.CREATED,
      });
    } catch (error) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Failed to log activity.',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getActivitiesByUser(
    userId: string,
  ): Promise<BaseResponse<UserActivity[] | null>> {
    try {
      const results = await this.prisma.userActivity.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 50,
      });

      return new BaseResponse({
        data: results,
        message: 'Fetched user activities successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Failed to get activity.',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }
}
