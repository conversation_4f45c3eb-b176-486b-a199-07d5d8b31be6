import { DateRangeFilterValue } from 'src/common/constants/dateRangeFilter.constant';
import { RecruitStatus } from 'src/common/enum/dashboard.enum';

export interface DashboardSummary {
  totalJobs: number;
  filterBy: string;
  totalActiveJobs: {
    current: number;
    change: number;
  };
  totalCandidates: {
    current: number;
    change: number;
  };
  jobSummaries: {
    jobId: number;
    clientId: number;
    clientName: string;
    jobTitle: string;
    jobStatus: string;
    started: Date | null;
    prospects: number;
    outreaches: number;
    replied: number;
    qualified: number;
  }[];
}

export interface DashboardFilterType {
  filterBy: DateRangeFilterValue;
  search?: string;
  status?: RecruitStatus | 'all';
  items_per_page?: number;
  page?: number;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
}
