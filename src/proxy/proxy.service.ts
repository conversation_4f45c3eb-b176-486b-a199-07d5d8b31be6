import { Injectable } from '@nestjs/common';
import { Request, Response } from 'express';
import type * as http from 'http';
import { createProxyMiddleware } from 'http-proxy-middleware';

@Injectable()
export class ProxyService {
  private readonly targetUrl =
    process.env.MASTER_API_URL || 'http://localhost:3001';

  async forwardRequest(req: Request, res: Response) {
    const proxyOptions: Parameters<typeof createProxyMiddleware>[0] = {
      target: this.targetUrl,
      changeOrigin: true,
      pathRewrite: {
        '^/proxy': '',
      },
      on: {
        proxyReq: (proxyReq, req: http.IncomingMessage) => {
          const apiKey = process.env.MASTER_API_KEY;
          if (apiKey) {
            proxyReq.setHeader('x-api-key', apiKey);
          }

          const cookie = (req as Request).headers['cookie'];
          if (cookie) {
            proxyReq.setHeader('cookie', cookie);
          }
        },
      },
    };

    const proxy = createProxyMiddleware(proxyOptions);
    return proxy(req, res, () => {});
  }
}
