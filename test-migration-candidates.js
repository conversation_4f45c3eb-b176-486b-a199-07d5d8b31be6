// Simple test script to verify the migration candidates to existing job API
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test data for migrating candidates to an existing job
const testCandidates = {
  candidates: [
    {
      firstName: 'Alice',
      lastName: '<PERSON>',
      linkedin: 'https://linkedin.com/in/alice<PERSON><PERSON><PERSON>-test-' + Date.now(),
      email: '<EMAIL>',
      title: 'Senior Software Engineer',
      company: 'Big Tech Corp',
      location: 'Seattle',
      summary: 'Senior software engineer with 8 years experience in distributed systems',
      note: 'Excellent candidate with strong leadership skills'
    },
    {
      firstName: 'Bob',
      lastName: '<PERSON>',
      linkedin: 'https://linkedin.com/in/bobwilson-test-' + Date.now(),
      email: '<EMAIL>',
      title: 'DevOps Engineer',
      company: 'Cloud Solutions Inc',
      location: 'Austin',
      summary: 'DevOps engineer specializing in AWS and Kubernetes',
      note: 'Great infrastructure knowledge'
    },
    {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      linkedin: 'https://linkedin.com/in/caroldavis-test-' + Date.now(),
      email: '<EMAIL>',
      title: 'Frontend Developer',
      company: 'Design Studio',
      location: 'Los Angeles',
      summary: 'Frontend developer with expertise in React and Vue.js',
      note: 'Strong UI/UX background'
    }
  ]
};

async function testMigrationCandidatesAPI(jobId) {
  try {
    console.log(`Testing Migration Candidates API for Job ID: ${jobId}...`);
    console.log('Test data:', JSON.stringify(testCandidates, null, 2));
    
    // Note: This test assumes you have authentication set up
    // You may need to add authentication headers
    const response = await axios.post(`${BASE_URL}/jobs/migration/${jobId}`, testCandidates, {
      headers: {
        'Content-Type': 'application/json',
        // Add authentication headers here if needed
        // 'Authorization': 'Bearer YOUR_TOKEN'
      }
    });
    
    console.log('✅ Migration Candidates API Test Successful!');
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.data) {
      const { job, candidatesResult } = response.data.data;
      console.log(`\n📊 Results Summary:`);
      console.log(`- Job: ${job ? job.title : 'Not found'}`);
      console.log(`- Candidates imported: ${candidatesResult.imported.length}`);
      console.log(`- Candidates failed: ${candidatesResult.failed.length}`);
      
      if (candidatesResult.failed.length > 0) {
        console.log('\n❌ Failed candidates:');
        candidatesResult.failed.forEach((failure, index) => {
          console.log(`  ${index + 1}. ${failure.candidate.firstName} ${failure.candidate.lastName}: ${failure.reason}`);
        });
      }
      
      if (candidatesResult.imported.length > 0) {
        console.log('\n✅ Successfully imported candidates:');
        candidatesResult.imported.forEach((candidate, index) => {
          console.log(`  ${index + 1}. ${candidate.firstName} ${candidate.lastName} (${candidate.linkedin})`);
        });
      }
    }
    
    return response.data;
    
  } catch (error) {
    console.error('❌ Migration Candidates API Test Failed!');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
    throw error;
  }
}

// Test with invalid job ID
async function testInvalidJobId() {
  try {
    console.log('\n\nTesting with invalid Job ID (999999)...');
    
    await axios.post(`${BASE_URL}/jobs/migration/999999`, testCandidates, {
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log('❌ Expected this test to fail, but it succeeded');
    
  } catch (error) {
    if (error.response && error.response.status === 404) {
      console.log('✅ Correctly returned 404 for invalid job ID');
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('❌ Unexpected error for invalid job ID test');
      console.error('Status:', error.response?.status);
      console.error('Data:', JSON.stringify(error.response?.data, null, 2));
    }
  }
}

// Test with empty candidates array
async function testEmptyCandidates(jobId) {
  try {
    console.log('\n\nTesting with empty candidates array...');
    
    const response = await axios.post(`${BASE_URL}/jobs/migration/${jobId}`, { candidates: [] }, {
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log('✅ Empty candidates test successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Empty candidates test failed!');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Get list of jobs to find a valid job ID for testing
async function getValidJobId() {
  try {
    const response = await axios.get(`${BASE_URL}/jobs/basic`);
    if (response.data.data && response.data.data.length > 0) {
      return response.data.data[0].id;
    }
    return null;
  } catch (error) {
    console.error('Failed to get jobs list:', error.message);
    return null;
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Migration Candidates API Tests...\n');
  
  // First, get a valid job ID
  const jobId = await getValidJobId();
  
  if (!jobId) {
    console.log('❌ No jobs found. Please create a job first or use the existing migration API.');
    console.log('You can run: node test-migration-job.js');
    return;
  }
  
  console.log(`📋 Using Job ID: ${jobId} for testing\n`);
  
  await testMigrationCandidatesAPI(jobId);
  await testInvalidJobId();
  await testEmptyCandidates(jobId);
  
  console.log('\n✨ Tests completed!');
}

// Check if server is running
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/`);
    return true;
  } catch (error) {
    console.log('⚠️  Server might not be running. Make sure to start the server first.');
    console.log('Run: npm run start:dev');
    return false;
  }
}

// Main execution
if (require.main === module) {
  checkServer().then(isRunning => {
    if (isRunning) {
      runTests();
    }
  });
}

module.exports = { testMigrationCandidatesAPI, testInvalidJobId, testEmptyCandidates };
