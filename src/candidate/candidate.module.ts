import { Module } from '@nestjs/common';
import { CandidateController } from './candidate.controller';
import { CandidateService } from './candidate.service';
import { PrismaService } from 'src/prisma.service';
import { NotificationModule } from 'src/notification/notification.module';
import { UserActivityModule } from 'src/user-activity/user-activity.module';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [NotificationModule, UserActivityModule, UserModule],
  controllers: [CandidateController],
  providers: [CandidateService, PrismaService],
})
export class CandidateModule {}
