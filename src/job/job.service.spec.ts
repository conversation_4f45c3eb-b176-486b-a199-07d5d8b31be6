import { Test, TestingModule } from '@nestjs/testing';
import { JobService } from './job.service';
import { PrismaService } from '../prisma.service';
import { NotificationService } from '../notification/notification.service';
import { UserActivityService } from '../user-activity/user-activity.service';
import { HttpStatus } from '@nestjs/common';
import { JobStatus, JobType, WorkLocation, SalaryCurrency, SalaryPeriod } from '@prisma/client';

describe('JobService', () => {
  let service: JobService;
  let prismaService: PrismaService;
  let notificationService: NotificationService;
  let userActivityService: UserActivityService;

  const mockPrismaService = {
    client: {
      findUnique: jest.fn(),
    },
    job: {
      create: jest.fn(),
      findUnique: jest.fn(),
    },
    candidate: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    jobCandidate: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    candidateNote: {
      create: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  const mockNotificationService = {
    broadcast: jest.fn(),
  };

  const mockUserActivityService = {
    logActivity: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
        {
          provide: UserActivityService,
          useValue: mockUserActivityService,
        },
      ],
    }).compile();

    service = module.get<JobService>(JobService);
    prismaService = module.get<PrismaService>(PrismaService);
    notificationService = module.get<NotificationService>(NotificationService);
    userActivityService = module.get<UserActivityService>(UserActivityService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('migrateJob', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
    };

    const mockRequest = {} as any;

    const mockMigrationJobDto = {
      title: 'Test Job',
      description: 'Test Description',
      clientId: 1,
      status: JobStatus.DRAFT,
      type: JobType.FULL_TIME,
      workLocation: WorkLocation.FULLY_ONSITE,
      keySkills: ['JavaScript'],
      responsibilities: ['Develop'],
      requirements: ['Experience'],
      benefits: ['Health'],
      salaryMin: 50000,
      salaryMax: 80000,
      salaryCurrency: SalaryCurrency.USD,
      salaryPeriod: SalaryPeriod.YEARLY,
      candidates: [
        {
          firstName: 'John',
          lastName: 'Doe',
          linkedin: 'https://linkedin.com/in/johndoe',
          email: '<EMAIL>',
          title: 'Developer',
          company: 'Tech Corp',
          location: 'NYC',
          summary: 'Experienced developer',
          note: 'Great candidate',
        },
      ],
    };

    it('should return error if client does not exist', async () => {
      mockPrismaService.client.findUnique.mockResolvedValue(null);

      const result = await service.migrateJob(mockRequest, mockMigrationJobDto, mockUser);

      expect(result.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(result.message).toContain('Client with ID 1 not found');
    });

    it('should successfully create job with candidates', async () => {
      const mockClient = { id: 1, name: 'Test Client' };
      const mockJob = { id: 1, title: 'Test Job', clientId: 1 };
      const mockCandidate = { id: 1, firstName: 'John', lastName: 'Doe', linkedin: 'https://linkedin.com/in/johndoe' };

      mockPrismaService.client.findUnique.mockResolvedValue(mockClient);
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return callback({
          job: {
            create: jest.fn().mockResolvedValue(mockJob),
            findUnique: jest.fn().mockResolvedValue({
              ...mockJob,
              client: mockClient,
              candidates: [],
            }),
          },
          candidate: {
            findUnique: jest.fn().mockResolvedValue(null),
            create: jest.fn().mockResolvedValue(mockCandidate),
          },
          jobCandidate: {
            findUnique: jest.fn().mockResolvedValue(null),
            create: jest.fn().mockResolvedValue({}),
          },
          candidateNote: {
            create: jest.fn().mockResolvedValue({}),
          },
        });
      });

      const result = await service.migrateJob(mockRequest, mockMigrationJobDto, mockUser);

      expect(result.statusCode).toBe(HttpStatus.CREATED);
      expect(result.message).toContain('Job migrated successfully');
      expect(mockUserActivityService.logActivity).toHaveBeenCalled();
      expect(mockNotificationService.broadcast).toHaveBeenCalled();
    });
  });
});
