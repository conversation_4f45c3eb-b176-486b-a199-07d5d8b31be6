/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { HttpStatus, Injectable } from '@nestjs/common';
import Stripe from 'stripe';
import { BaseResponse } from 'src/common/dto/base-response.dto';

@Injectable()
export class PaymentService {
  private stripe: Stripe;

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_TEST_SECRET_KEY!);
  }

  async createSetupIntent(): Promise<
    BaseResponse<{ clientSecret: string } | null>
  > {
    try {
      const setupIntent = await this.stripe.setupIntents.create({
        usage: 'off_session',
      });

      return new BaseResponse({
        data: { clientSecret: setupIntent.client_secret! },
        message: 'SetupIntent created successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async createCustomer({
    email,
    name,
    company,
  }: {
    email: string;
    name?: string;
    company: string;
  }): Promise<BaseResponse<{ customerId: string } | null>> {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name: name || '',
        metadata: {
          company: company || '',
        },
      });

      return new BaseResponse({
        data: { customerId: customer.id },
        message: 'Customer created successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async attachPaymentMethodToCustomer({
    paymentMethodId,
    customerId,
  }: {
    paymentMethodId: string;
    customerId: string;
  }): Promise<BaseResponse<null>> {
    try {
      await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      await this.stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });

      return new BaseResponse({
        data: null,
        message: 'Payment method attached successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async createSubscription({
    customerId,
    plan,
  }: {
    customerId: string;
    plan?: 'starter' | 'pro';
  }): Promise<BaseResponse<{ subscriptionId: string } | null>> {
    try {
      const priceId =
        plan === 'starter'
          ? process.env.STRIPE_TEST_PRICE_ID_STARTER
          : process.env.STRIPE_TEST_PRICE_ID_PRO;
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        trial_period_days: 7,
        payment_behavior: 'default_incomplete',
        expand: ['latest_invoice.payment_intent'],
      });

      return new BaseResponse({
        data: {
          subscriptionId: subscription.id,
        },
        message: 'Subscription created successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }
}
