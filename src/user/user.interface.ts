export type UserRole = 'SUPER_ADMIN' | 'ADMIN' | 'TEAM';
export type CountryCode = 'US' | 'CA' | 'UK' | 'AU' | 'DE' | 'FR' | 'JP';
export type Timezone =
  | 'PACIFIC'
  | 'MOUNTAIN'
  | 'CENTRAL'
  | 'EASTERN'
  | 'GMT'
  | 'CET'
  | 'AEST';
export type UserStatus = 'ACTIVE' | 'UPGRADING' | 'BLOCKED' | 'DELETED';
export type UserPlan = 'FREE' | 'TRIAL' | 'STARTER' | 'PRO';

export interface User {
  id: string;
  email: string;
  password: string;
  companyName: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
  providerCustomerId?: string;
  jobTitle?: string;
  phone?: string;
  bio?: string;
  country: CountryCode;
  timezone: Timezone;
  credits: number;
  status: UserStatus;
  plan: UserPlan;
  createdAt: Date;
  updatedAt: Date;
}
