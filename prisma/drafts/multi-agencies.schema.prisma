generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  AGENCY
  TEAM
}

enum CountryCode {
  US
  CA
  UK
  AU
  DE
  FR
  JP
}

enum JobType {
  FULL_TIME
  PART_TIME
  CONTRACT
  TEMPORARY
  INTERNSHIP
}

enum WorkLocation {
  FULLY_ONSITE
  HYBRID
  REMOTE
}

enum JobStatus {
  DRAFT
  ACTIVE
  PAUSED
  CLOSED
}

enum ClientStatus {
  ACTIVE
  INACTIVE
  PROSPECT
}

enum CandidateStatus {
  PROSPECT
  MATCHED
  CONTACTED
  INTERVIEWING
  TECHNICAL_TEST
  REFERENCE_CHECK
  OFFER
  HIRED
  REJECTED
  ON_HOLD
}

enum CandidateQualification {
  QUALIFIED
  NOT_QUALIFIED
}

enum SalaryCurrency {
  USD
  EUR
  GBP
  CAD
  AUD
  JPY
  CNY
  INR
}

enum SalaryPeriod {
  HOURLY
  MONTHLY
  YEARLY
}

model Agency {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  users       User[]
  clients     Client[]
  jobs        Job[]
  candidates  Candidate[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime? @updatedAt
}

model User {
  id         Int        @id @default(autoincrement())
  email      String     @unique
  password   String
  firstName  String
  lastName   String
  role       UserRole   @default(TEAM)
  jobTitle   String?
  phone      String?
  bio        String?
  country    CountryCode @default(US)
  agency     Agency?    @relation(fields: [agencyId], references: [id])
  agencyId   Int?

  Job        Job[]      @relation("UserJobs")
  Client     Client[]   @relation("UserClients")
  Candidate  Candidate[] @relation("UserCandidates")

  createdAt  DateTime   @default(now())
  updatedAt  DateTime?  @updatedAt
}

model Client {
  id          Int          @id @default(autoincrement())
  companyName String
  email       String       @unique
  industry    String
  phone       String?
  location    String?
  summary     String?      @db.Text
  website     String?
  status      ClientStatus @default(PROSPECT)

  agency      Agency       @relation(fields: [agencyId], references: [id])
  agencyId    Int

  createdBy   User         @relation("UserClients", fields: [createdById], references: [id])
  createdById Int

  jobs        Job[]
  createdAt   DateTime     @default(now())
  updatedAt   DateTime?    @updatedAt
}

model Job {
  id               Int            @id @default(autoincrement())
  title            String
  description      String         @db.Text
  client           Client         @relation(fields: [clientId], references: [id])
  clientId         Int
  department       String?
  location         String?
  type             JobType        @default(FULL_TIME)
  workLocation     WorkLocation   @default(FULLY_ONSITE)
  keySkills        String[]
  responsibilities String[]
  requirements     String[]
  benefits         String[]
  salaryMin        Int?           @default(0)
  salaryMax        Int?           @default(0)
  salaryCurrency   SalaryCurrency @default(USD)
  salaryPeriod     SalaryPeriod   @default(MONTHLY)
  status           JobStatus      @default(DRAFT)
  startDate        DateTime?
  endDate          DateTime?

  candidates       JobCandidate[]
  agency           Agency         @relation(fields: [agencyId], references: [id])
  agencyId         Int

  createdBy        User           @relation("UserJobs", fields: [createdById], references: [id])
  createdById      Int

  createdAt        DateTime       @default(now())
  updatedAt        DateTime?      @updatedAt
}

model Candidate {
  id           Int            @id @default(autoincrement())
  name         String
  title        String
  summary      String         @db.Text
  phone        String?
  linkedin     String?
  skills       String[]
  hourlyRate   Int?
  availability String?

  agency       Agency         @relation(fields: [agencyId], references: [id])
  agencyId     Int

  createdBy    User           @relation("UserCandidates", fields: [createdById], references: [id])
  createdById  Int

  jobs         JobCandidate[]

  createdAt    DateTime       @default(now())
  updatedAt    DateTime?      @updatedAt
}

model JobCandidate {
  id          Int                     @id @default(autoincrement())
  job         Job                     @relation(fields: [jobId], references: [id])
  jobId       Int
  candidate   Candidate               @relation(fields: [candidateId], references: [id])
  candidateId Int
  status      CandidateStatus         @default(PROSPECT)
  qualified   CandidateQualification?
  appliedAt   DateTime                @default(now())

  @@unique([jobId, candidateId])
}