/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

const planMap = {
  trial: 'Free Trial',
  starter: 'Starter',
  pro: 'Pro',
};

@Injectable()
export class EmailService {
  private transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.GMAIL_USER,
      pass: process.env.GMAIL_PASS,
    },
  });

  async sendNewAgencyNotification(
    email: string,
    companyName: string,
  ): Promise<void> {
    const mailOptions = {
      from: `"TalentLabs Recruit Platform" <${process.env.GMAIL_USER}>`,
      to: [
        process.env.PRIMARY_ADMIN_EMAIL,
        process.env.SECONDARY_ADMIN_EMAIL,
        process.env.SLACK_NOTIFICATION_EMAIL,
      ],
      subject: '[DASH Intake Platform] New Agency Registration Notification',
      text: `Hello,

A new agency has just registered on the DASH Intake Platform.

Details:
- Email: ${email}
- Company Name: ${companyName}

Please review and proceed with the necessary steps.

Best regards,
Recruit Platform Notification Service`,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`Notification sent for new agency: ${email}`);
    } catch (error) {
      console.error('Failed to send email notification:', error);
      throw error;
    }
  }

  async sendUpgradeRequestNotification(
    email: string,
    companyName: string,
    plan: 'trial' | 'starter' | 'pro',
  ): Promise<void> {
    const planLabel = planMap[plan] || 'Free Trial';
    const mailOptions = {
      from: `"TalentLabs Recruit Platform" <${process.env.GMAIL_USER}>`,
      to: [
        process.env.PRIMARY_ADMIN_EMAIL,
        process.env.SECONDARY_ADMIN_EMAIL,
        process.env.SLACK_NOTIFICATION_EMAIL,
      ],
      subject: '[DASH Intake Platform] Agency Upgrade Request Notification',
      text: `Hello,

An agency has requested to upgrade their account on the DASH Intake Platform.

Details:
- Email: ${email}
- Company Name: ${companyName}
- Requested Plan: ${planLabel}

Please review and proceed with the necessary steps.

Best regards,
Recruit Platform Notification Service`,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`Upgrade request notification sent for agency: ${email}`);
    } catch (error) {
      console.error(
        'Failed to send upgrade request email notification:',
        error,
      );
      throw error;
    }
  }

  async sendEnterpriseContactNotification(
    name: string,
    email: string,
    phone: string,
    company: string,
    expectedCandidatesPerWeek: string,
    expectedRolesPerMonth: string,
  ): Promise<void> {
    const mailOptions = {
      from: `"TalentLabs Recruit Platform" <${process.env.GMAIL_USER}>`,
      to: [
        process.env.PRIMARY_ADMIN_EMAIL,
        process.env.SECONDARY_ADMIN_EMAIL,
        process.env.SLACK_NOTIFICATION_EMAIL,
      ],
      subject: '[DASH Intake Platform] Enterprise Contact Request',
      text: `Hello,

A new enterprise contact form has been submitted on the DASH Intake Platform.

Details:
- Name: ${name}
- Email: ${email}
- Phone: ${phone}
- Company: ${company}
- Expected Candidates Per Week: ${expectedCandidatesPerWeek}
- Expected Roles Per Month: ${expectedRolesPerMonth}

Please follow up with the user as needed.

Best regards,
Recruit Platform Notification Service`,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`Enterprise contact notification sent for: ${email}`);
    } catch (error) {
      console.error(
        'Failed to send enterprise contact email notification:',
        error,
      );
      throw error;
    }
  }
}
