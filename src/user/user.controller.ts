import { Request } from 'express';
import { Body, Controller, Put, UseGuards, Get, Req } from '@nestjs/common';
import { UpdateUserDto } from '../user/dtos/user.dto';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { UserService } from 'src/user/user.service';
import { User } from 'src/user/user.interface';

@Controller('user')
export class UserController {
  constructor(private userService: UserService) {}

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@Req() req: Request): Promise<BaseResponse<User | null>> {
    return this.userService.getProfile(req);
  }

  @UseGuards(JwtAuthGuard)
  @Put('profile')
  updateProfile(
    @Body() body: UpdateUserDto,
    @Req() req: Request,
  ): Promise<BaseResponse<User | null>> {
    return this.userService.updateProfile(body, req);
  }
}
