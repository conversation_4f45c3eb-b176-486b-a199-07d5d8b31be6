import { Module, ValidationPipe } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { APP_PIPE } from '@nestjs/core';
import { JobModule } from './job/job.module';
import { CandidateModule } from './candidate/candidate.module';
import { ClientModule } from './client/client.module';
import { JobCandidateModule } from './job-candidate/job-candidate.module';
import { UserModule } from './user/user.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { UserActivityModule } from './user-activity/user-activity.module';
import { NotificationModule } from './notification/notification.module';
import { EmailModule } from './email/email.module';
import { PaymentModule } from './payment/payment.module';
import { ProxyModule } from './proxy/proxy.module';

@Module({
  imports: [
    AuthModule,
    JobModule,
    CandidateModule,
    ClientModule,
    JobCandidateModule,
    UserModule,
    DashboardModule,
    UserActivityModule,
    NotificationModule,
    EmailModule,
    PaymentModule,
    ProxyModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
  ],
})
export class AppModule {}
