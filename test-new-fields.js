// Test script to verify the new fields in migration APIs
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test data with new fields
const testData = {
  client: {
    name: 'Test Company for New Fields',
    email: `test-new-fields-${Date.now()}@example.com`,
    industry: 'Technology'
  },
  job: {
    title: 'Test Job for New Fields',
    description: 'Testing new candidate fields',
    status: 'DRAFT',
    type: 'FULL_TIME',
    workLocation: 'HYBRID'
  },
  candidates: [
    {
      firstName: 'Test',
      lastName: 'Candidate',
      linkedin: `https://linkedin.com/in/test-candidate-${Date.now()}`,
      email: '<EMAIL>',
      title: 'Software Engineer',
      company: 'Previous Company',
      location: 'San Francisco, CA',
      summary: 'Test candidate with comprehensive data',
      skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'AWS'],
      phone: '******-0199',
      hourlyRate: 80,
      availability: 'Available in 2 weeks',
      note: 'General candidate note for testing',
      qualified: 'QUALIFIED',
      jobNote: 'Job-specific note: Perfect fit for this role with excellent technical skills'
    }
  ]
};

async function testNewFields() {
  try {
    console.log('🧪 Testing Migration API with New Fields...\n');
    
    // Test 1: Create job with candidate using new fields
    console.log('📝 Test 1: Creating job with candidate (new fields)');
    const response = await axios.post(`${BASE_URL}/jobs/migration`, testData, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ Job creation successful!');
    console.log('Response status:', response.status);
    
    if (response.data.data) {
      const { job, candidatesResult } = response.data.data;
      console.log(`📊 Job created: ${job.title} (ID: ${job.id})`);
      console.log(`👥 Candidates imported: ${candidatesResult.imported.length}`);
      console.log(`❌ Candidates failed: ${candidatesResult.failed.length}`);
      
      if (candidatesResult.imported.length > 0) {
        const candidate = candidatesResult.imported[0];
        console.log('\n📋 Candidate Details:');
        console.log(`- Name: ${candidate.firstName} ${candidate.lastName}`);
        console.log(`- Skills: ${candidate.skills ? candidate.skills.join(', ') : 'None'}`);
        console.log(`- Phone: ${candidate.phone || 'Not provided'}`);
        console.log(`- Hourly Rate: $${candidate.hourlyRate || 'Not provided'}`);
        console.log(`- Availability: ${candidate.availability || 'Not provided'}`);
      }
      
      // Test 2: Add more candidates to the existing job
      if (job && job.id) {
        console.log('\n📝 Test 2: Adding candidates to existing job');
        
        const additionalCandidates = {
          candidates: [
            {
              firstName: 'Additional',
              lastName: 'Candidate',
              linkedin: `https://linkedin.com/in/additional-candidate-${Date.now()}`,
              email: '<EMAIL>',
              title: 'Senior Developer',
              skills: ['Python', 'Django', 'PostgreSQL', 'Redis'],
              phone: '******-0299',
              hourlyRate: 90,
              availability: 'Available immediately',
              note: 'Additional candidate for testing',
              qualified: 'MAYBE',
              jobNote: 'Good candidate but needs more frontend experience'
            }
          ]
        };
        
        const response2 = await axios.post(`${BASE_URL}/jobs/migration/${job.id}`, additionalCandidates, {
          headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ Additional candidates migration successful!');
        console.log('Response status:', response2.status);
        
        if (response2.data.data) {
          const { candidatesResult: result2 } = response2.data.data;
          console.log(`👥 Additional candidates imported: ${result2.imported.length}`);
          console.log(`❌ Additional candidates failed: ${result2.failed.length}`);
        }
      }
    }
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed!');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test with minimal data to ensure backward compatibility
async function testBackwardCompatibility() {
  try {
    console.log('\n🔄 Testing Backward Compatibility...\n');
    
    const minimalData = {
      client: {
        name: 'Minimal Test Company',
        email: `minimal-test-${Date.now()}@example.com`,
        industry: 'Technology'
      },
      job: {
        title: 'Minimal Test Job',
        description: 'Testing backward compatibility',
        status: 'DRAFT',
        type: 'FULL_TIME',
        workLocation: 'REMOTE'
      },
      candidates: [
        {
          firstName: 'Minimal',
          lastName: 'Candidate',
          linkedin: `https://linkedin.com/in/minimal-candidate-${Date.now()}`
        }
      ]
    };
    
    const response = await axios.post(`${BASE_URL}/jobs/migration`, minimalData, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ Backward compatibility test successful!');
    console.log('Response status:', response.status);
    console.log('✅ Old API format still works without new fields');
    
  } catch (error) {
    console.error('❌ Backward compatibility test failed!');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Check if server is running
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/`);
    return true;
  } catch (error) {
    console.log('⚠️  Server might not be running. Make sure to start the server first.');
    console.log('Run: npm run start:dev');
    return false;
  }
}

// Main execution
async function runTests() {
  const isRunning = await checkServer();
  if (isRunning) {
    await testNewFields();
    await testBackwardCompatibility();
    
    console.log('\n📝 Notes:');
    console.log('- All new fields are optional and backward compatible');
    console.log('- Skills, phone, hourlyRate, and availability are stored in Candidate table');
    console.log('- qualified and jobNote are stored in JobCandidate table');
    console.log('- Make sure to run database migration before testing');
  }
}

if (require.main === module) {
  runTests();
}

module.exports = { testNewFields, testBackwardCompatibility };
