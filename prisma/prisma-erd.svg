<?xml version="1.0" standalone="no"?>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" id="mermaid-svg" width="undefined" xmlns="http://www.w3.org/2000/svg" height="undefined" style="max-width: 487.845703125px;" viewBox="0 0 487.845703125 1492"><style>#mermaid-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#mermaid-svg .error-icon{fill:#552222;}#mermaid-svg .error-text{fill:#552222;stroke:#552222;}#mermaid-svg .edge-thickness-normal{stroke-width:2px;}#mermaid-svg .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg .marker{fill:#333333;}#mermaid-svg .marker.cross{stroke:#333333;}#mermaid-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg .entityBox{fill:#ECECFF;stroke:#9370DB;}#mermaid-svg .attributeBoxOdd{fill:#ffffff;stroke:#9370DB;}#mermaid-svg .attributeBoxEven{fill:#f2f2f2;stroke:#9370DB;}#mermaid-svg .relationshipLabelBox{fill:hsl(80,100%,96.2745098039%);opacity:0.7;background-color:hsl(80,100%,96.2745098039%);}#mermaid-svg .relationshipLabelBox rect{opacity:0.5;}#mermaid-svg .relationshipLine{stroke:#333333;}#mermaid-svg:root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-svg er{fill:apa;}</style><g/><defs><marker id="ONLY_ONE_START" refX="0" refY="9" markerWidth="18" markerHeight="18" orient="auto"><path stroke="gray" fill="none" d="M9,0 L9,18 M15,0 L15,18"/></marker></defs><defs><marker id="ONLY_ONE_END" refX="18" refY="9" markerWidth="18" markerHeight="18" orient="auto"><path stroke="gray" fill="none" d="M3,0 L3,18 M9,0 L9,18"/></marker></defs><defs><marker id="ZERO_OR_ONE_START" refX="0" refY="9" markerWidth="30" markerHeight="18" orient="auto"><circle stroke="gray" fill="white" cx="21" cy="9" r="6"/><path stroke="gray" fill="none" d="M9,0 L9,18"/></marker></defs><defs><marker id="ZERO_OR_ONE_END" refX="30" refY="9" markerWidth="30" markerHeight="18" orient="auto"><circle stroke="gray" fill="white" cx="9" cy="9" r="6"/><path stroke="gray" fill="none" d="M21,0 L21,18"/></marker></defs><defs><marker id="ONE_OR_MORE_START" refX="18" refY="18" markerWidth="45" markerHeight="36" orient="auto"><path stroke="gray" fill="none" d="M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"/></marker></defs><defs><marker id="ONE_OR_MORE_END" refX="27" refY="18" markerWidth="45" markerHeight="36" orient="auto"><path stroke="gray" fill="none" d="M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18"/></marker></defs><defs><marker id="ZERO_OR_MORE_START" refX="18" refY="18" markerWidth="57" markerHeight="36" orient="auto"><circle stroke="gray" fill="white" cx="48" cy="18" r="6"/><path stroke="gray" fill="none" d="M0,18 Q18,0 36,18 Q18,36 0,18"/></marker></defs><defs><marker id="ZERO_OR_MORE_END" refX="39" refY="18" markerWidth="57" markerHeight="36" orient="auto"><circle stroke="gray" fill="white" cx="9" cy="18" r="6"/><path stroke="gray" fill="none" d="M21,18 Q39,0 57,18 Q39,36 21,18"/></marker></defs><path class="er relationshipLine" d="M81.453125,1084L81.453125,1092.3333333333333C81.453125,1100.6666666666667,81.453125,1117.3333333333333,97.84244791666667,1145.3598664473047C114.23177083333333,1173.3863995612762,147.01041666666666,1212.7727991225522,163.39973958333334,1232.4659989031904L179.7890625,1252.1591986838284" stroke="gray" fill="none" marker-end="url(#ONLY_ONE_END)" marker-start="url(#ZERO_OR_MORE_START)"/><path class="er relationshipLine" d="M95.76634530141844,718L93.38080858451536,726.3333333333334C90.99527186761229,734.6666666666666,86.22419843380614,751.3333333333334,83.83866171690308,768C81.453125,784.6666666666666,81.453125,801.3333333333334,81.453125,809.6666666666666L81.453125,818" stroke="gray" fill="none" marker-end="url(#ONLY_ONE_END)" marker-start="url(#ZERO_OR_MORE_START)"/><path class="er relationshipLine" d="M228.59302969858157,718L230.97856641548466,726.3333333333334C233.36410313238773,734.6666666666666,238.13517656619388,751.3333333333334,240.52071328309694,790.1666666666666C242.90625,829,242.90625,890,242.90625,951C242.90625,1012,242.90625,1073,242.90625,1111.8333333333333C242.90625,1150.6666666666667,242.90625,1167.3333333333333,242.90625,1175.6666666666667L242.90625,1184" stroke="gray" fill="none" marker-end="url(#ONLY_ONE_END)" marker-start="url(#ZERO_OR_MORE_START)"/><path class="er relationshipLine" d="M410.724609375,1084L410.724609375,1092.3333333333333C410.724609375,1100.6666666666667,410.724609375,1117.3333333333333,393.2744140625,1145.839297976095C375.82421875,1174.3452626188564,340.923828125,1214.6905252377128,323.4736328125,1234.863156547141L306.0234375,1255.0357878565692" stroke="gray" fill="none" marker-end="url(#ONLY_ONE_END)" marker-start="url(#ZERO_OR_MORE_START)"/><path class="er relationshipLine" d="M200.32405181623932,154L193.96665776353277,162.33333333333334C187.6092637108262,170.66666666666666,174.89447560541308,187.33333333333334,168.53708155270655,204C162.1796875,220.66666666666666,162.1796875,237.33333333333334,162.1796875,245.66666666666666L162.1796875,254" stroke="gray" fill="none" marker-end="url(#ONLY_ONE_END)" marker-start="url(#ZERO_OR_MORE_START)"/><path class="er relationshipLine" d="M337.81640625,150.44726871436455L349.9677734375,159.37272392863713C362.119140625,168.2981791429097,386.421875,186.14908957145485,398.5732421875,242.07454478572743C410.724609375,298,410.724609375,392,410.724609375,486C410.724609375,580,410.724609375,674,410.724609375,729.3333333333334C410.724609375,784.6666666666666,410.724609375,801.3333333333334,410.724609375,809.6666666666666L410.724609375,818" stroke="gray" fill="none" marker-end="url(#ONLY_ONE_END)" marker-start="url(#ZERO_OR_MORE_START)"/><g id="User" transform="translate(179.7890625,1184 )"><rect class="er entityBox" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="0" width="126.234375" height="288"/><text class="er entityLabel" id="entity-User" x="0" y="0" dominant-baseline="middle" text-anchor="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px" transform="translate(63.1171875,12)">User</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="24" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-1-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,35)">Int</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="24" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-1-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,35)">id</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="46" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-2-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,57)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="46" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-2-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,57)">email</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="68" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-3-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,79)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="68" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-3-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,79)">password</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="90" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-4-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,101)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="90" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-4-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,101)">firstName</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="112" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-5-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,123)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="112" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-5-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,123)">lastName</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="134" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-6-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,145)">UserRole</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="134" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-6-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,145)">role</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="156" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-7-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,167)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="156" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-7-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,167)">jobTitle</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="178" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-8-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,189)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="178" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-8-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,189)">phone</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="200" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-9-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,211)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="200" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-9-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,211)">bio</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="222" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-10-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,233)">CountryCode</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="222" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-10-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,233)">country</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="244" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-11-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,255)">DateTime</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="244" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-11-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,255)">createdAt</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="266" width="68.5859375" height="22"/><text class="er entityLabel" id="entity-User-attr-12-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,277)">DateTime</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="068.5859375" y="266" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-User-attr-12-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(73.5859375,277)">updatedAt</text></g><g id="Client" transform="translate(20,818 )"><rect class="er entityBox" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="0" width="122.90625" height="266"/><text class="er entityLabel" id="entity-Client" x="0" y="0" dominant-baseline="middle" text-anchor="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px" transform="translate(61.453125,12)">Client</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="24" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-1-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,35)">Int</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="24" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-1-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,35)">id</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="46" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-2-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,57)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="46" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-2-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,57)">name</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="68" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-3-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,79)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="68" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-3-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,79)">email</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="90" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-4-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,101)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="90" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-4-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,101)">industry</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="112" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-5-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,123)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="112" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-5-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,123)">phone</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="134" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-6-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,145)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="134" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-6-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,145)">location</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="156" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-7-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,167)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="156" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-7-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,167)">summary</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="178" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-8-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,189)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="178" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-8-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,189)">website</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="200" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-9-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,211)">ClientStatus</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="200" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-9-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,211)">status</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="222" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-10-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,233)">DateTime</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="222" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-10-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,233)">createdAt</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="244" width="65.2578125" height="22"/><text class="er entityLabel" id="entity-Client-attr-11-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,255)">DateTime</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="065.2578125" y="244" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-Client-attr-11-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(70.2578125,255)">updatedAt</text></g><g id="Job" transform="translate(83.6640625,254 )"><rect class="er entityBox" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="0" width="157.03125" height="464"/><text class="er entityLabel" id="entity-Job" x="0" y="0" dominant-baseline="middle" text-anchor="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px" transform="translate(78.515625,12)">Job</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="24" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-1-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,35)">Int</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="24" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-1-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,35)">id</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="46" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-2-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,57)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="46" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-2-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,57)">title</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="68" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-3-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,79)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="68" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-3-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,79)">description</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="90" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-4-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,101)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="90" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-4-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,101)">department</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="112" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-5-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,123)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="112" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-5-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,123)">location</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="134" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-6-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,145)">JobType</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="134" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-6-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,145)">type</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="156" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-7-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,167)">WorkLocation</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="156" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-7-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,167)">workLocation</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="178" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-8-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,189)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="178" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-8-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,189)">keySkills</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="200" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-9-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,211)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="200" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-9-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,211)">responsibilities</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="222" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-10-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,233)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="222" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-10-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,233)">requirements</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="244" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-11-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,255)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="244" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-11-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,255)">benefits</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="266" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-12-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,277)">Int</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="266" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-12-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,277)">salaryMin</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="288" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-13-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,299)">Int</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="288" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-13-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,299)">salaryMax</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="310" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-14-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,321)">SalaryCurrency</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="310" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-14-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,321)">salaryCurrency</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="332" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-15-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,343)">SalaryPeriod</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="332" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-15-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,343)">salaryPeriod</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="354" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-16-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,365)">JobStatus</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="354" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-16-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,365)">status</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="376" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-17-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,387)">DateTime</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="376" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-17-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,387)">startDate</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="398" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-18-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,409)">DateTime</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="398" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-18-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,409)">endDate</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="420" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-19-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,431)">DateTime</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="420" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-19-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,431)">createdAt</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="442" width="78.453125" height="22"/><text class="er entityLabel" id="entity-Job-attr-20-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,453)">DateTime</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="078.453125" y="442" width="78.578125" height="22"/><text class="er entityLabel" id="entity-Job-attr-20-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(83.453125,453)">updatedAt</text></g><g id="Candidate" transform="translate(353.603515625,818 )"><rect class="er entityBox" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="0" width="114.2421875" height="266"/><text class="er entityLabel" id="entity-Candidate" x="0" y="0" dominant-baseline="middle" text-anchor="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px" transform="translate(57.12109375,12)">Candidate</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="24" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-1-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,35)">Int</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="24" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-1-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,35)">id</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="46" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-2-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,57)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="46" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-2-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,57)">name</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="68" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-3-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,79)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="68" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-3-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,79)">title</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="90" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-4-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,101)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="90" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-4-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,101)">summary</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="112" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-5-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,123)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="112" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-5-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,123)">phone</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="134" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-6-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,145)">String</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="134" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-6-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,145)">linkedin</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="156" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-7-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,167)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="156" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-7-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,167)">skills</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="178" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-8-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,189)">Int</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="178" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-8-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,189)">hourlyRate</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="200" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-9-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,211)">String</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="200" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-9-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,211)">availability</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="222" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-10-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,233)">DateTime</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="222" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-10-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,233)">createdAt</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="244" width="53.6640625" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-11-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,255)">DateTime</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="053.6640625" y="244" width="60.578125" height="22"/><text class="er entityLabel" id="entity-Candidate-attr-11-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(58.6640625,255)">updatedAt</text></g><g id="JobCandidate" transform="translate(165.05859375,20 )"><rect class="er entityBox" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="0" width="172.7578125" height="134"/><text class="er entityLabel" id="entity-JobCandidate" x="0" y="0" dominant-baseline="middle" text-anchor="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px" transform="translate(86.37890625,12)">JobCandidate</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="24" width="115.109375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-1-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,35)">Int</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0115.109375" y="24" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-1-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(120.109375,35)">id</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="46" width="115.109375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-2-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,57)">CandidateStatus</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0115.109375" y="46" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-2-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(120.109375,57)">status</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="68" width="115.109375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-3-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,79)">CandidateQualification</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0115.109375" y="68" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-3-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(120.109375,79)">qualified</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="90" width="115.109375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-4-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,101)">DateTime</text><rect class="er attributeBoxEven" fill="honeydew" fill-opacity="100%" stroke="gray" x="0115.109375" y="90" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-4-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(120.109375,101)">createdAt</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0" y="112" width="115.109375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-5-type" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(5,123)">DateTime</text><rect class="er attributeBoxOdd" fill="honeydew" fill-opacity="100%" stroke="gray" x="0115.109375" y="112" width="57.6484375" height="22"/><text class="er entityLabel" id="entity-JobCandidate-attr-5-name" x="0" y="0" dominant-baseline="middle" text-anchor="left" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 10.2px" transform="translate(120.109375,123)">updatedAt</text></g><rect class="er relationshipLabelBox" x="90.28707885742188" y="1167.5018310546875" width="54.15625" height="14" fill="white" fill-opacity="85%"/><text class="er relationshipLabel" id="rel1" x="117.36520385742188" y="1174.5018310546875" text-anchor="middle" dominant-baseline="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px">createdBy</text><rect class="er relationshipLabelBox" x="68.55241394042969" y="760.3355712890625" width="30.765625" height="14" fill="white" fill-opacity="85%"/><text class="er relationshipLabel" id="rel2" x="83.93522644042969" y="767.3355712890625" text-anchor="middle" dominant-baseline="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px">client</text><rect class="er relationshipLabelBox" x="215.8280029296875" y="943.3914794921875" width="54.15625" height="14" fill="white" fill-opacity="85%"/><text class="er relationshipLabel" id="rel3" x="242.9061279296875" y="950.3914794921875" text-anchor="middle" dominant-baseline="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px">createdBy</text><rect class="er relationshipLabelBox" x="344.8196716308594" y="1169.3253173828125" width="54.15625" height="14" fill="white" fill-opacity="85%"/><text class="er relationshipLabel" id="rel4" x="371.8977966308594" y="1176.3253173828125" text-anchor="middle" dominant-baseline="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px">createdBy</text><rect class="er relationshipLabelBox" x="161.43077087402344" y="192.9638214111328" width="17.53125" height="14" fill="white" fill-opacity="85%"/><text class="er relationshipLabel" id="rel5" x="170.19639587402344" y="199.9638214111328" text-anchor="middle" dominant-baseline="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px">job</text><rect class="er relationshipLabelBox" x="384.120849609375" y="465.98443603515625" width="53.203125" height="14" fill="white" fill-opacity="85%"/><text class="er relationshipLabel" id="rel6" x="410.722412109375" y="472.98443603515625" text-anchor="middle" dominant-baseline="middle" style="font-family: &quot;trebuchet ms&quot;, verdana, arial, sans-serif;; font-size: 12px">candidate</text></svg>