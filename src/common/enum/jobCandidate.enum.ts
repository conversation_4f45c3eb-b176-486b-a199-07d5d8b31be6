export enum CandidateStatus {
  PROSPECT = 'PROSPECT',
  MATCHED = 'MATCHED',
  CONTACTED = 'CONTACTED',
  INTERVIEWING = 'INTERVIEWING',
  TECHNICAL_TEST = 'TECHNICAL_TEST',
  REFERENCE_CHECK = 'REFERENCE_CHECK',
  OFFER = 'OFFER',
  HIRED = 'HIRED',
  REJECTED = 'REJECTED',
  ON_HOLD = 'ON_HOLD',
}

export const CandidateStatusLabels: Record<CandidateStatus, string> = {
  [CandidateStatus.PROSPECT]: 'Prospect',
  [CandidateStatus.MATCHED]: 'Matched',
  [CandidateStatus.CONTACTED]: 'Contacted',
  [CandidateStatus.INTERVIEWING]: 'Interviewing',
  [CandidateStatus.TECHNICAL_TEST]: 'Technical Test',
  [CandidateStatus.REFERENCE_CHECK]: 'Reference Check',
  [CandidateStatus.OFFER]: 'Offer',
  [CandidateStatus.HIRED]: 'Hired',
  [CandidateStatus.REJECTED]: 'Rejected',
  [CandidateStatus.ON_HOLD]: 'On Hold',
};

export enum CandidateQualification {
  QUALIFIED = 'QUALIFIED',
  NOT_QUALIFIED = 'NOT_QUALIFIED',
}

export const CandidateQualificationLabels: Record<
  CandidateQualification,
  string
> = {
  [CandidateQualification.QUALIFIED]: 'Qualified',
  [CandidateQualification.NOT_QUALIFIED]: 'Not Qualified',
};
