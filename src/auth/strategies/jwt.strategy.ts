import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { Request } from 'express';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    const accessTokenKey = process.env.ACCESS_TOKEN_KEY;

    if (!accessTokenKey) {
      throw new Error(
        'ACCESS_TOKEN_KEY is not defined in environment variables',
      );
    }

    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        (req: Request) => {
          const cookies = req.cookies as { accessToken?: string };
          return cookies?.accessToken || null;
        },
      ]),
      secretOrKey: accessTokenKey,
    });
  }

  validate(payload: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  }) {
    return {
      id: payload.id,
      email: payload.email,
      firstName: payload.firstName,
      lastName: payload.lastName,
    };
  }
}
