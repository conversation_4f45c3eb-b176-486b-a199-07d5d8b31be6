version: "3.9"
services:
  nest_app_dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nest_app_dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      NODE_ENV: development
      DATABASE_URL: **************************************/dbname
    depends_on:
      - postgres
    env_file:
      - .env
    command: npm run start:dev

  postgres:
    image: postgres:16-alpine
    container_name: postgres
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: dbname
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
