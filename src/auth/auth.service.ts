import { Request } from 'express';
import { HttpStatus, Injectable } from '@nestjs/common';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { UserSession } from 'src/user/dtos/user.dto';
import { getClientHostFromRequest } from 'src/utils/request.util';
import { getMasterApiUrl, getMasterApiKey } from 'src/configs/config';

@Injectable()
export class AuthService {
  constructor() {}

  refreshAccessToken = async (
    req: Request,
    refreshToken: string,
  ): Promise<BaseResponse<{ accessToken: string } | null>> => {
    try {
      const response = await fetch(`${getMasterApiUrl()}/auth/refresh`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': getMasterApiKey(),
          'x-client-host': getClientHostFromRequest(req),
          Cookie: `refreshToken=${refreshToken}`,
        } as HeadersInit,
      });

      const json = await response.json();

      if (!response.ok || !json?.data) {
        return new BaseResponse({
          data: null,
          message: json?.message || 'Refresh failed',
          statusCode: response.status,
        });
      }

      return new BaseResponse({
        data: json.data,
        message: 'Token refreshed successfully',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  };

  login = async (
    req: Request,
    data: {
      email: string;
      password: string;
    },
  ): Promise<
    BaseResponse<{ accessToken: string; refreshToken: string } | null>
  > => {
    try {
      const response = await fetch(`${getMasterApiUrl()}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': getMasterApiKey(),
          'x-client-host': getClientHostFromRequest(req),
        } as HeadersInit,
        body: JSON.stringify(data),
      });

      const json = await response.json();

      if (!response.ok || !json?.data) {
        return new BaseResponse({
          data: null,
          message: json?.message || 'Login failed',
          statusCode: HttpStatus.UNAUTHORIZED,
        });
      }

      return new BaseResponse({
        data: json.data,
        message: 'Login successful',
        statusCode: json?.message?.statusCode || HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  };

  changePassword = async (
    userId: string,
    oldPassword: string,
    newPassword: string,
    req: Request,
  ): Promise<BaseResponse<null>> => {
    try {
      const cookieHeader = req.headers.cookie;
      const response = await fetch(
        `${getMasterApiUrl()}/auth/change-password`,
        {
          method: 'POST',
          headers: {
            Cookie: cookieHeader,
            'Content-Type': 'application/json',
            'x-api-key': getMasterApiKey(),
            'x-client-host': getClientHostFromRequest(req),
          } as HeadersInit,
          body: JSON.stringify({ userId, oldPassword, newPassword }),
        },
      );

      const json = await response.json();

      return new BaseResponse({
        statusCode: json?.statusCode || HttpStatus.OK,
        message: json?.message || 'Change password sucessfully',
        data: null,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  };

  getMe = async (req: Request): Promise<BaseResponse<UserSession | null>> => {
    try {
      const cookieHeader = req.headers.cookie;
      const response = await fetch(`${getMasterApiUrl()}/auth/me`, {
        method: 'GET',
        headers: {
          Cookie: cookieHeader,
          'Content-Type': 'application/json',
          'x-api-key': getMasterApiKey(),
          'x-client-host': getClientHostFromRequest(req),
        } as HeadersInit,
      });

      const json = await response.json();

      if (!json?.data) {
        return new BaseResponse({
          data: null,
          message:
            json?.message || 'Authentication failed. Please try logging in.',
          statusCode: json?.statusCode || HttpStatus.UNAUTHORIZED,
        });
      }

      return new BaseResponse({
        data: json.data,
        message: 'User info fetched successfully',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  };
}
