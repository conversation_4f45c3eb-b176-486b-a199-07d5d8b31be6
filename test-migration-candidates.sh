#!/bin/bash

# Migration Candidates to Existing Job API Test Script using cURL
# Tests the POST /jobs/migration/:jobId endpoint

BASE_URL="http://localhost:3000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Testing Migration Candidates to Existing Job API${NC}"
echo "Base URL: ${BASE_URL}"
echo "Endpoint: POST /jobs/migration/:jobId"
echo ""

# Function to get a valid job ID
get_job_id() {
  echo -e "${YELLOW}Getting a valid job ID for testing...${NC}"
  
  JOB_RESPONSE=$(curl -s "${BASE_URL}/jobs/basic" -H "Accept: application/json")
  
  # Extract the first job ID using basic text processing
  JOB_ID=$(echo "$JOB_RESPONSE" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
  
  if [ -z "$JOB_ID" ]; then
    echo -e "${RED}❌ No jobs found. Please create a job first.${NC}"
    echo "You can run: ./test-migration-job-updated.sh"
    exit 1
  fi
  
  echo -e "${GREEN}✅ Found Job ID: ${JOB_ID}${NC}"
  echo ""
}

# Get a valid job ID first
get_job_id

ENDPOINT="/jobs/migration/${JOB_ID}"
TIMESTAMP=$(date +%s)

# Test 1: Complete migration with multiple candidates including skills
echo -e "${YELLOW}Test 1: Complete Migration (Multiple Candidates with Skills)${NC}"
echo "----------------------------------------"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"candidates\": [
      {
        \"firstName\": \"Alice\",
        \"lastName\": \"Johnson\",
        \"linkedin\": \"https://linkedin.com/in/alice-johnson-migration-${TIMESTAMP}\",
        \"email\": \"<EMAIL>\",
        \"title\": \"Senior Software Engineer\",
        \"company\": \"Big Tech Corp\",
        \"location\": \"Seattle, WA\",
        \"summary\": \"Senior software engineer with 8 years experience in distributed systems and cloud architecture.\",
        \"skills\": [\"JavaScript\", \"TypeScript\", \"React\", \"Node.js\", \"AWS\", \"Docker\", \"Kubernetes\"],
        \"phone\": \"******-0101\",
        \"hourlyRate\": 85,
        \"availability\": \"Available immediately\",
        \"note\": \"Excellent candidate with strong leadership skills and extensive cloud experience.\",
        \"qualified\": \"QUALIFIED\",
        \"jobNote\": \"Perfect senior-level candidate with excellent leadership and technical skills\"
      },
      {
        \"firstName\": \"Bob\",
        \"lastName\": \"Wilson\",
        \"linkedin\": \"https://linkedin.com/in/bob-wilson-devops-${TIMESTAMP}\",
        \"email\": \"<EMAIL>\",
        \"title\": \"DevOps Engineer\",
        \"company\": \"Cloud Solutions Inc\",
        \"location\": \"Austin, TX\",
        \"summary\": \"DevOps engineer specializing in AWS infrastructure and CI/CD pipelines.\",
        \"skills\": [\"AWS\", \"Kubernetes\", \"Docker\", \"Terraform\", \"Jenkins\", \"Python\", \"Bash\"],
        \"phone\": \"******-0102\",
        \"hourlyRate\": 75,
        \"availability\": \"Available in 2 weeks\",
        \"note\": \"Great infrastructure knowledge and automation expertise.\",
        \"qualified\": \"QUALIFIED\",
        \"jobNote\": \"Excellent DevOps skills, perfect for infrastructure role\"
      },
      {
        \"firstName\": \"Carol\",
        \"lastName\": \"Davis\",
        \"linkedin\": \"https://linkedin.com/in/carol-davis-frontend-${TIMESTAMP}\",
        \"email\": \"<EMAIL>\",
        \"title\": \"Frontend Developer\",
        \"company\": \"Design Studio\",
        \"location\": \"Los Angeles, CA\",
        \"summary\": \"Frontend developer with expertise in modern JavaScript frameworks and UI/UX design.\",
        \"skills\": [\"React\", \"Vue.js\", \"TypeScript\", \"CSS\", \"HTML\", \"Figma\", \"Sass\"],
        \"phone\": \"******-0103\",
        \"hourlyRate\": 65,
        \"availability\": \"Available part-time\",
        \"note\": \"Strong UI/UX background with excellent design sense.\",
        \"qualified\": \"MAYBE\",
        \"jobNote\": \"Good frontend skills but part-time availability might be limiting\"
      }
    ]
  }" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo -e "\n${GREEN}✅ Test 1 completed${NC}\n"

# Test 2: Single candidate without optional fields
echo -e "${YELLOW}Test 2: Single Candidate (Minimal Data)${NC}"
echo "----------------------------------------"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"candidates\": [
      {
        \"firstName\": \"David\",
        \"lastName\": \"Brown\",
        \"linkedin\": \"https://linkedin.com/in/david-brown-minimal-${TIMESTAMP}\",
        \"title\": \"Software Developer\"
      }
    ]
  }" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo -e "\n${GREEN}✅ Test 2 completed${NC}\n"

# Test 3: Empty candidates array
echo -e "${YELLOW}Test 3: Empty Candidates Array${NC}"
echo "----------------------------------------"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"candidates\": []
  }" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo -e "\n${GREEN}✅ Test 3 completed${NC}\n"

# Test 4: Invalid job ID
echo -e "${YELLOW}Test 4: Invalid Job ID (Should Fail)${NC}"
echo "----------------------------------------"

curl -X POST "${BASE_URL}/jobs/migration/999999" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"candidates\": [
      {
        \"firstName\": \"Test\",
        \"lastName\": \"User\",
        \"linkedin\": \"https://linkedin.com/in/test-invalid-job-${TIMESTAMP}\"
      }
    ]
  }" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo -e "\n${RED}❌ Test 4 completed (Expected to fail with 404)${NC}\n"

# Test 5: Invalid candidate data (missing linkedin)
echo -e "${YELLOW}Test 5: Invalid Candidate Data (Should Partially Fail)${NC}"
echo "----------------------------------------"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"candidates\": [
      {
        \"firstName\": \"Valid\",
        \"lastName\": \"Candidate\",
        \"linkedin\": \"https://linkedin.com/in/valid-candidate-${TIMESTAMP}\"
      },
      {
        \"firstName\": \"Invalid\",
        \"lastName\": \"Candidate\"
      }
    ]
  }" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo -e "\n${YELLOW}⚠️  Test 5 completed (Expected partial success)${NC}\n"

echo -e "${BLUE}🎉 All tests completed!${NC}"
echo ""
echo -e "${YELLOW}Notes:${NC}"
echo "- Make sure your server is running on ${BASE_URL}"
echo "- The API migrates candidates to existing jobs by job ID"
echo "- Existing candidates (by LinkedIn) will be updated and linked to the job"
echo "- New candidates will be created and linked to the job"
echo "- Authentication headers may be required depending on your setup"
echo "- Check the server logs for detailed information"
echo "- Job ID used for testing: ${JOB_ID}"
