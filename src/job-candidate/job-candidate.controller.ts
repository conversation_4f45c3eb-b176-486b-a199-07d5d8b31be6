import {
  Body,
  Param,
  Controller,
  Put,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  CandidateQualification,
  CandidateStatus,
  JobCandidate,
} from '@prisma/client';
import { JobCandidateService } from '../job-candidate/job-candidate.service';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';

@Controller('job-candidates')
export class JobCandidateController {
  constructor(private readonly jobCandidateService: JobCandidateService) {}

  @Put(':jobId/:candidateId')
  @UseGuards(JwtAuthGuard)
  updateStatus(
    @Body()
    data: { status?: CandidateStatus; qualified?: CandidateQualification; note?: string },
    @Param('jobId', ParseIntPipe) jobId: number,
    @Param('candidateId', ParseIntPipe) candidateId: number,
  ): Promise<BaseResponse<JobCandidate | null>> {
    return this.jobCandidateService.update({ jobId, candidateId, ...data });
  }
}
