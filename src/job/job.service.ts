import { Injectable, HttpStatus } from '@nestjs/common';
import { Prisma, Job, JobStatus, NotificationType, Candidate, CandidateStatus, ClientStatus } from '@prisma/client';
import { Request } from 'express';
import {
  CreateJobDto,
  JobFilterType,
  JobPaginationResponseType,
  UpdateJobDto,
  MigrationJobDto,
  MigrationJobResponseType,
  MigrationJobDataDto,
} from 'src/job/dtos/job.dto';
import { PrismaService } from 'src/prisma.service';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { UserSession } from 'src/user/dtos/user.dto';
import { NotificationService } from 'src/notification/notification.service';
import { UserActivityService } from 'src/user-activity/user-activity.service';
import { CreateCandidateDto } from 'src/candidate/dtos/candidate.dto';
import { ActivityAction, ActivityObject } from '@prisma/client';

@Injectable()
export class JobService {
  constructor(
    private prismaService: PrismaService,
    private notificationService: NotificationService,
    private userActivityService: UserActivityService,
  ) {}

  async create(
    req: Request,
    body: CreateJobDto,
    loggedUser: UserSession,
  ): Promise<BaseResponse<Job | null>> {
    try {
      const result = await this.prismaService.$transaction(async (tx) => {
        const { id } = await tx.job.create({
          data: {
            ...body,
            createdById: loggedUser.id,
          },
        });

        return tx.job.findUnique({
          where: { id },
          // TODO, Master API integration
          // include: {
          //   createdBy: {
          //     select: {
          //       id: true,
          //       email: true,
          //       firstName: true,
          //       lastName: true,
          //       jobTitle: true,
          //       phone: true,
          //     },
          //   },
          // },
        });
      });

      if (!result) {
        return new BaseResponse({
          data: null,
          message: 'Job creation failed or job not found.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        });
      }

      const metadata = {
        jobId: result.id,
        jobTitle: result.title,
      };

      await this.userActivityService.logActivity({
        userId: loggedUser.id,
        action: 'CREATE',
        objectType: 'JOB',
        objectId: String(result.id),
        metadata,
      });

      await this.notificationService.broadcast(req, {
        title: `New job created: "${result.title}"`,
        content: `${loggedUser.firstName || ''} ${
          loggedUser.lastName || ''
        } created job "${body.title}"`,
        type: NotificationType.JOB_CREATED,
        // excludeUserId: loggedUser.id, // TODO, maybe use later
        metadata,
      });

      return new BaseResponse({
        data: result,
        message: 'Job created successfully.',
        statusCode: HttpStatus.CREATED,
      });
    } catch (error: any) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      if (error.code === 'P2003') {
        // Prisma error code for Foreign Key violation
        return new BaseResponse({
          data: null,
          message: 'Client not found. Invalid clientId provided.',
          statusCode: HttpStatus.BAD_REQUEST,
        });
      }

      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getAll(
    filters: JobFilterType,
  ): Promise<BaseResponse<JobPaginationResponseType | null>> {
    try {
      const items_per_page = Number(filters.items_per_page) || 10;
      const page = Number(filters.page) || 1;
      const search = filters.search?.trim().toLowerCase() || '';
      const status = filters.status || '';
      const clientId = filters.clientId ? Number(filters.clientId) : 0;
      const skip = page > 1 ? (page - 1) * items_per_page : 0;

      const whereClause: Prisma.JobWhereInput = {
        OR: [
          {
            title: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            description: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
        ],
        ...(status && {
          AND: [{ status: { equals: status } }],
        }),
        ...(clientId > 0 && {
          AND: [
            ...(status ? [{ status: { equals: status } }] : []),
            { clientId },
          ],
        }),
      };

      const jobs = await this.prismaService.job.findMany({
        take: items_per_page,
        skip,
        where: whereClause,
        include: {
          client: {
            select: {
              id: true,
              name: true,
              email: true,
              industry: true,
            },
          },
          // TODO, Master API integration
          // createdBy: {
          //   select: {
          //     id: true,
          //     email: true,
          //     firstName: true,
          //     lastName: true,
          //     jobTitle: true,
          //     phone: true,
          //   },
          // },
          candidates: {
            include: {
              candidate: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      const total = await this.prismaService.job.count({
        where: whereClause,
      });

      const response: JobPaginationResponseType = {
        data: jobs,
        total,
        currentPage: page,
        itemsPerPage: items_per_page,
      };

      return new BaseResponse({
        data: response,
        message: 'Get all jobs successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getDetail(id: number): Promise<BaseResponse<Job | null>> {
    try {
      const job = await this.prismaService.job.findFirst({
        where: {
          id,
        },
        include: {
          // TODO, Master API integration
          // createdBy: {
          //   select: {
          //     id: true,
          //     email: true,
          //     firstName: true,
          //     lastName: true,
          //     jobTitle: true,
          //     phone: true,
          //   },
          // },
          client: {
            select: {
              id: true,
              name: true,
              email: true,
              industry: true,
            },
          },
          candidates: {
            include: {
              candidate: {
                include: {
                  jobs: {
                    include: {
                      job: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!job) {
        return new BaseResponse({
          data: null,
          message: `Job with ID ${id} not found.`,
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      return new BaseResponse({
        data: job,
        message: 'Get job successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async update(
    id: number,
    data: UpdateJobDto,
    loggedUser: UserSession,
  ): Promise<BaseResponse<Job | null>> {
    try {
      const { clientId, status, ...rest } = data;

      const job = await this.prismaService.job.findFirst({
        where: { id },
      });

      if (!job) {
        return new BaseResponse({
          data: null,
          message: `Job with ID ${id} not found.`,
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      if (clientId) {
        const clientExists = await this.prismaService.client.findUnique({
          where: { id: clientId },
        });

        if (!clientExists) {
          return new BaseResponse({
            data: null,
            message: `Client with ID ${clientId} not found.`,
            statusCode: HttpStatus.NOT_FOUND,
          });
        }
      }

      const updateData: Prisma.JobUpdateInput = {
        ...rest,
        status,
        ...(clientId && { client: { connect: { id: clientId } } }),
      };

      if (status === JobStatus.ACTIVE && job.status !== JobStatus.ACTIVE) {
        updateData['activatedAt'] = new Date();
      }

      const updatedJob = await this.prismaService.job.update({
        where: { id },
        data: updateData,
        include: {
          // TODO, Master API integration
          // createdBy: {
          //   select: {
          //     id: true,
          //     email: true,
          //     firstName: true,
          //     lastName: true,
          //     jobTitle: true,
          //     phone: true,
          //   },
          // },
          candidates: {
            include: {
              candidate: true,
            },
          },
        },
      });

      await this.userActivityService.logActivity({
        userId: loggedUser.id,
        action: 'UPDATE',
        objectType: 'JOB',
        objectId: String(id),
        metadata: {
          jobId: updatedJob.id,
          jobTitle: updatedJob.title,
          updatedFields: Object.keys(data),
        },
      });

      return new BaseResponse({
        data: updatedJob,
        message: 'Job updated successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async delete(
    jobId: number,
    loggedUser: UserSession,
  ): Promise<BaseResponse<null>> {
    try {
      const result = await this.prismaService.$transaction(async (tx) => {
        const job = await tx.job.findUnique({
          where: { id: jobId },
        });

        if (!job) {
          return null;
        }

        await tx.jobCandidate.deleteMany({
          where: { jobId },
        });

        await tx.job.delete({
          where: { id: jobId },
        });

        await this.userActivityService.logActivity({
          userId: loggedUser.id,
          action: 'DELETE',
          objectType: 'JOB',
          objectId: String(jobId),
          metadata: {
            jobId: job.id,
            jobTitle: job.title,
          },
        });

        return true;
      });

      if (!result) {
        return new BaseResponse({
          data: null,
          message: 'Job not found.',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      return new BaseResponse({
        data: null,
        message: 'Job deleted successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getAllBasic(
    status?: JobStatus,
  ): Promise<BaseResponse<{ id: number; title: string; status: JobStatus }[]>> {
    try {
      const whereClause: Prisma.JobWhereInput = status ? { status } : {};

      const jobs = await this.prismaService.job.findMany({
        where: whereClause,
        select: {
          id: true,
          title: true,
          department: true,
          location: true,
          status: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return new BaseResponse({
        data: jobs,
        message: 'Get basic job list successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: [],
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async migrateJob(
    req: Request,
    body: MigrationJobDto,
    loggedUser: UserSession,
  ): Promise<BaseResponse<MigrationJobResponseType | null>> {
    try {
      const { client: clientData, job: jobData, candidates } = body;

      const result = await this.prismaService.$transaction(async (tx) => {
        // Step 1: Handle client - check if exists by email, create if not
        let client = await tx.client.findUnique({
          where: { email: clientData.email },
        });

        let isNewClient = false;
        if (!client) {
          // Create new client with ACTIVE status as default
          client = await tx.client.create({
            data: {
              ...clientData,
              status: ClientStatus.ACTIVE, // Default status as requested
              industry: clientData.industry || '', // Ensure required field has value
              createdById: loggedUser.id,
            },
          });
          isNewClient = true;
        }

        // Step 2: Create the job with the client ID
        const job = await tx.job.create({
          data: {
            ...jobData,
            clientId: client.id,
            createdById: loggedUser.id,
          },
        });

        // Process candidates if provided
        const imported: Candidate[] = [];
        const failed: {
          candidate: CreateCandidateDto;
          reason: string;
          status: string;
        }[] = [];

        if (candidates && candidates.length > 0) {
          for (const candidateData of candidates) {
            const { firstName, lastName, linkedin, note, ...rest } = candidateData;

            // Validate required fields (linkedin is required)
            if (!linkedin) {
              failed.push({
                candidate: candidateData,
                reason: 'Missing required field: linkedin.',
                status: 'error',
              });
              continue;
            }

            try {
              // Check if candidate already exists
              const existing = await tx.candidate.findUnique({
                where: { linkedin },
              });

              let candidate: Candidate;

              if (existing) {
                // Update existing candidate
                candidate = await tx.candidate.update({
                  where: { id: existing.id },
                  data: {
                    firstName,
                    lastName,
                    ...rest,
                  },
                });
              } else {
                // Create new candidate
                candidate = await tx.candidate.create({
                  data: {
                    firstName,
                    lastName,
                    linkedin,
                    ...rest,
                    createdById: loggedUser.id,
                  },
                });
              }

              // Create job-candidate relationship (check if it already exists)
              const existingJobCandidate = await tx.jobCandidate.findUnique({
                where: {
                  jobId_candidateId: {
                    jobId: job.id,
                    candidateId: candidate.id,
                  },
                },
              });

              if (!existingJobCandidate) {
                await tx.jobCandidate.create({
                  data: {
                    jobId: job.id,
                    candidateId: candidate.id,
                    status: CandidateStatus.PROSPECT,
                    createdById: loggedUser.id,
                  },
                });
              }

              // Add candidate note if provided
              if (note && note.trim()) {
                await tx.candidateNote.create({
                  data: {
                    content: note.trim(),
                    candidateId: candidate.id,
                    createdById: loggedUser.id,
                  },
                });
              }

              imported.push(candidate);
            } catch (candidateError: any) {
              failed.push({
                candidate: candidateData,
                reason: candidateError.message || 'Failed to process candidate',
                status: 'error',
              });
            }
          }
        }

        return {
          client: {
            id: client.id,
            name: client.name,
            email: client.email,
            isNew: isNewClient,
          },
          job: await tx.job.findUnique({
            where: { id: job.id },
            include: {
              client: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  industry: true,
                },
              },
              candidates: {
                include: {
                  candidate: true,
                },
              },
            },
          }),
          candidatesResult: {
            imported,
            failed,
          },
        };
      });

      // Log activity
      await this.userActivityService.logActivity({
        userId: loggedUser.id,
        action: ActivityAction.CREATE,
        objectType: ActivityObject.JOB,
        objectId: result.job?.id.toString() || 'unknown',
        metadata: {
          title: result.job?.title,
          clientId: result.client?.id,
          clientName: result.client?.name,
          isNewClient: result.client?.isNew,
          candidatesImported: result.candidatesResult.imported.length,
          candidatesFailed: result.candidatesResult.failed.length,
        },
      });

      // Send notification
      if (result.job) {
        const clientInfo = result.client?.isNew ? `new client "${result.client.name}"` : `client "${result.client?.name}"`;
        const candidateInfo = result.candidatesResult.imported.length > 0
          ? ` with ${result.candidatesResult.imported.length} candidate(s)`
          : '';

        await this.notificationService.broadcast(req, {
          title: 'Job migrated successfully',
          content: `Job "${result.job.title}" created for ${clientInfo}${candidateInfo}`,
          type: NotificationType.JOB_CREATED,
          metadata: {
            jobId: result.job.id,
            clientId: result.client?.id,
            isNewClient: result.client?.isNew,
            candidateCount: result.candidatesResult.imported.length,
          },
        });
      }

      const clientMessage = result.client?.isNew ? 'New client created and job' : 'Job';
      const candidateMessage = candidates && candidates.length > 0
        ? ` ${result.candidatesResult.imported.length} candidates imported, ${result.candidatesResult.failed.length} failed.`
        : '';
      const message = `${clientMessage} migrated successfully.${candidateMessage}`;

      return new BaseResponse({
        data: result,
        message,
        statusCode: HttpStatus.CREATED,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }
}
