export enum CountryCode {
  US = 'US',
  CA = 'CA',
  UK = 'UK',
  AU = 'AU',
  DE = 'DE',
  FR = 'FR',
  JP = 'JP',
}

export const CountryLabel: Record<CountryCode, string> = {
  [CountryCode.US]: 'United States',
  [CountryCode.CA]: 'Canada',
  [CountryCode.UK]: 'United Kingdom',
  [CountryCode.AU]: 'Australia',
  [CountryCode.DE]: 'Germany',
  [CountryCode.FR]: 'France',
  [CountryCode.JP]: 'Japan',
};
