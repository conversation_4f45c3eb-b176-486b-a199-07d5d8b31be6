// Simple test script to verify the migration job API
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test data
const testMigrationJob = {
  title: 'Test Migration Job',
  description: 'A test job created via migration API',
  clientId: 1, // Assuming client with ID 1 exists
  status: 'DRAFT',
  type: 'FULL_TIME',
  workLocation: 'FULLY_ONSITE',
  keySkills: ['JavaScript', 'Node.js', 'NestJS'],
  responsibilities: ['Develop APIs', 'Write tests'],
  requirements: ['3+ years experience', 'Bachelor degree'],
  benefits: ['Health insurance', 'Remote work'],
  salaryMin: 50000,
  salaryMax: 80000,
  salaryCurrency: 'USD',
  salaryPeriod: 'YEARLY',
  candidates: [
    {
      firstName: 'John',
      lastName: 'Doe',
      linkedin: 'https://linkedin.com/in/johndoe-test-' + Date.now(),
      email: '<EMAIL>',
      title: 'Software Engineer',
      company: 'Tech Corp',
      location: 'New York',
      summary: 'Experienced software engineer with 5 years in web development',
      skills: ['JavaScript', 'React', 'Node.js', 'MongoDB', 'Express'],
      phone: '******-0201',
      hourlyRate: 70,
      availability: 'Available immediately',
      note: 'Great candidate for this position',
      qualified: 'QUALIFIED',
      jobNote: 'Perfect fit for senior role, strong technical background'
    },
    {
      firstName: 'Jane',
      lastName: 'Smith',
      linkedin: 'https://linkedin.com/in/janesmith-test-' + Date.now(),
      email: '<EMAIL>',
      title: 'Full Stack Developer',
      company: 'Startup Inc',
      location: 'San Francisco',
      summary: 'Full stack developer with React and Node.js expertise',
      skills: ['React', 'Node.js', 'TypeScript', 'PostgreSQL', 'GraphQL'],
      phone: '******-0202',
      hourlyRate: 80,
      availability: 'Available in 1 week',
      note: 'Strong technical background',
      qualified: 'MAYBE',
      jobNote: 'Good candidate but needs more backend experience'
    }
  ]
};

async function testMigrationJobAPI() {
  try {
    console.log('Testing Migration Job API...');
    console.log('Test data:', JSON.stringify(testMigrationJob, null, 2));
    
    // Note: This test assumes you have authentication set up
    // You may need to add authentication headers
    const response = await axios.post(`${BASE_URL}/jobs/migration`, testMigrationJob, {
      headers: {
        'Content-Type': 'application/json',
        // Add authentication headers here if needed
        // 'Authorization': 'Bearer YOUR_TOKEN'
      }
    });
    
    console.log('✅ Migration Job API Test Successful!');
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.data) {
      const { job, candidatesResult } = response.data.data;
      console.log(`\n📊 Results Summary:`);
      console.log(`- Job created: ${job ? job.title : 'Failed'}`);
      console.log(`- Candidates imported: ${candidatesResult.imported.length}`);
      console.log(`- Candidates failed: ${candidatesResult.failed.length}`);
      
      if (candidatesResult.failed.length > 0) {
        console.log('\n❌ Failed candidates:');
        candidatesResult.failed.forEach((failure, index) => {
          console.log(`  ${index + 1}. ${failure.candidate.firstName} ${failure.candidate.lastName}: ${failure.reason}`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Migration Job API Test Failed!');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test with minimal data (job only, no candidates)
async function testJobOnlyMigration() {
  try {
    console.log('\n\nTesting Job-Only Migration (no candidates)...');
    
    const jobOnlyData = {
      title: 'Job Only Test',
      description: 'Testing job creation without candidates',
      clientId: 1,
      status: 'DRAFT',
      type: 'FULL_TIME',
      workLocation: 'FULLY_ONSITE'
    };
    
    const response = await axios.post(`${BASE_URL}/jobs/migration`, jobOnlyData, {
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log('✅ Job-Only Migration Test Successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Job-Only Migration Test Failed!');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Migration Job API Tests...\n');
  
  await testMigrationJobAPI();
  await testJobOnlyMigration();
  
  console.log('\n✨ Tests completed!');
}

// Check if server is running
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/health`);
    return true;
  } catch (error) {
    console.log('⚠️  Server might not be running. Make sure to start the server first.');
    console.log('Run: npm run start:dev');
    return false;
  }
}

// Main execution
if (require.main === module) {
  checkServer().then(isRunning => {
    if (isRunning) {
      runTests();
    }
  });
}

module.exports = { testMigrationJobAPI, testJobOnlyMigration };
