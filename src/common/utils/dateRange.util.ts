/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-call */

import { subDays, subMonths, subYears, startOfDay, endOfDay } from 'date-fns';
import { DateRangeFilterValue } from 'src/common/constants/dateRangeFilter.constant';

export function getDateRange(
  filterBy: DateRangeFilterValue,
): { from: Date; to: Date } | null {
  const now = new Date();

  switch (filterBy) {
    case 'today':
      return { from: startOfDay(now), to: endOfDay(now) };
    case 'last_7_days':
      return { from: subDays(now, 7), to: now };
    case 'last_14_days':
      return { from: subDays(now, 14), to: now };
    case 'last_30_days':
      return { from: subDays(now, 30), to: now };
    case 'last_90_days':
      return { from: subDays(now, 90), to: now };
    case 'last_6_months':
      return { from: subMonths(now, 6), to: now };
    case 'last_1_year':
      return { from: subYears(now, 1), to: now };
    case 'all_time':
    default:
      return null;
  }
}
