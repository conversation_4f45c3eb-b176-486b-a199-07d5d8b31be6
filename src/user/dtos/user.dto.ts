import { CountryCode, Timezone } from '@prisma/client';
import {
  IsNotEmpty,
  IsOptional,
  IsEmail,
  Matches,
  IsEnum,
} from 'class-validator';

export interface UserFilterType {
  items_per_page?: number;
  page?: number;
  search?: string;
  country?: CountryCode;
}

export class UpdateUserDto {
  @IsOptional()
  email: string;

  @IsOptional()
  firstName: string;

  @IsOptional()
  lastName: string;

  @IsOptional()
  jobTitle: string;

  @IsOptional()
  // @Matches(/^\+?[1-9]\d{0,2}[ .-]?\(?\d{3}\)?[ .-]?\d{3}[ .-]?\d{4}$/, {
  //   message: 'Phone number must be in a valid international format',
  // })
  phone: string;

  @IsOptional()
  bio: string;

  @IsOptional()
  @IsEnum(CountryCode)
  country: CountryCode;

  @IsOptional()
  @IsEnum(Timezone)
  timezone: Timezone;
}

export type UserSession = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  accessToken: string;
};
