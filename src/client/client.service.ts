import { Injectable, HttpStatus } from '@nestjs/common';
import { Prisma, Client, ClientStatus } from '@prisma/client';
import {
  CreateClientDto,
  ClientFilterType,
  ClientPaginationResponseType,
  UpdateClientDto,
} from 'src/client/dtos/client.dto';
import { PrismaService } from 'src/prisma.service';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { UserSession } from 'src/user/dtos/user.dto';
import { UserActivityService } from 'src/user-activity/user-activity.service';

@Injectable()
export class ClientService {
  constructor(
    private prismaService: PrismaService,
    private userActivityService: UserActivityService,
  ) {}

  async create(
    body: CreateClientDto,
    loggedUser: UserSession,
  ): Promise<BaseResponse<Client | null>> {
    try {
      const existingClient = await this.prismaService.client.findFirst({
        where: {
          email: body.email,
        },
      });

      if (existingClient) {
        return new BaseResponse({
          data: null,
          message: 'Client with this email already exists.',
          statusCode: HttpStatus.BAD_REQUEST,
        });
      }

      const newClient = await this.prismaService.client.create({
        data: {
          ...body,
          createdById: loggedUser.id,
        },
      });

      await this.userActivityService.logActivity({
        userId: loggedUser.id,
        action: 'CREATE',
        objectType: 'CLIENT',
        objectId: String(newClient.id),
        metadata: {
          clientId: newClient.id,
          clientName: newClient.name,
        },
      });

      return new BaseResponse({
        data: newClient,
        message: 'Client created successfully.',
        statusCode: HttpStatus.CREATED,
      });
    } catch (error: any) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
      if (error.code === 'P2002' && error.meta?.target.includes('email')) {
        // Prisma error code for unique constraint violation
        return new BaseResponse({
          data: null,
          message:
            'Client email already exists. Please use a different email address.',
          statusCode: HttpStatus.BAD_REQUEST,
        });
      }

      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getAll(
    filters: ClientFilterType,
  ): Promise<BaseResponse<ClientPaginationResponseType | null>> {
    try {
      const items_per_page = Number(filters.items_per_page) || 10;
      const page = Number(filters.page) || 1;
      const search = filters.search?.trim().toLowerCase() || '';
      const status = filters.status || '';
      const skip = page > 1 ? (page - 1) * items_per_page : 0;

      const whereClause = {
        OR: [
          {
            name: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            email: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
        ],
        ...(status && {
          AND: [{ status: { equals: status } }],
        }),
      };

      const clients = await this.prismaService.client.findMany({
        take: items_per_page,
        skip,
        where: whereClause,
        include: {
          jobs: true,
          // TODO, Master API integration
          // createdBy: {
          //   select: {
          //     id: true,
          //     email: true,
          //     firstName: true,
          //     lastName: true,
          //     phone: true,
          //   },
          // },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      const total = await this.prismaService.client.count({
        where: whereClause,
      });

      return new BaseResponse({
        data: {
          data: clients,
          total,
          currentPage: page,
          itemsPerPage: items_per_page,
        },
        message: 'Get all clients successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getAllBasic(
    status?: ClientStatus,
  ): Promise<
    BaseResponse<{ id: number; name: string; status: ClientStatus }[]>
  > {
    try {
      const whereClause: Prisma.ClientWhereInput = status ? { status } : {};

      const clients = await this.prismaService.client.findMany({
        where: whereClause,
        select: {
          id: true,
          name: true,
          status: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return new BaseResponse({
        data: clients,
        message: 'Get basic client list successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: [],
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getDetail(id: number): Promise<BaseResponse<Client | null>> {
    try {
      const client = await this.prismaService.client.findFirst({
        where: {
          id,
        },
        include: {
          jobs: {
            include: {
              candidates: {
                include: {
                  candidate: true,
                },
              },
            },
          },
          // TODO, Master API integration
          // createdBy: {
          //   select: {
          //     id: true,
          //     email: true,
          //     firstName: true,
          //     lastName: true,
          //     phone: true,
          //   },
          // },
        },
      });

      if (!client) {
        return new BaseResponse({
          data: null,
          message: `Client with ID ${id} not found.`,
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      return new BaseResponse({
        data: client,
        message: 'Get client successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async update(
    id: number,
    data: UpdateClientDto,
    loggedUser: UserSession,
  ): Promise<BaseResponse<Client | null>> {
    try {
      if (data.email) {
        const existingClient = await this.prismaService.client.findFirst({
          where: {
            email: data.email,
            NOT: {
              id,
            },
          },
        });

        if (existingClient) {
          return new BaseResponse({
            data: null,
            message:
              'Client email already exists. Please use a different email address.',
            statusCode: HttpStatus.BAD_REQUEST,
          });
        }
      }

      const updatedClient = await this.prismaService.client.update({
        where: {
          id,
        },
        data: {
          ...data,
        },
        // TODO, Master API integration
        // include: {
        //   createdBy: {
        //     select: {
        //       id: true,
        //       email: true,
        //       firstName: true,
        //       lastName: true,
        //       phone: true,
        //     },
        //   },
        // },
      });

      await this.userActivityService.logActivity({
        userId: loggedUser.id,
        action: 'UPDATE',
        objectType: 'CLIENT',
        objectId: String(updatedClient.id),
        metadata: {
          clientId: updatedClient.id,
          clientName: updatedClient.name,
          updatedFields: Object.keys(data),
        },
      });

      return new BaseResponse({
        data: updatedClient,
        message: 'Client updated successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async delete(
    id: number,
    loggedUser: UserSession,
  ): Promise<BaseResponse<null>> {
    try {
      const client = await this.prismaService.client.findUnique({
        where: { id },
      });

      if (!client) {
        return new BaseResponse({
          data: null,
          message: `Client with ID ${id} not found.`,
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      await this.prismaService.$transaction(async (tx) => {
        await tx.job.deleteMany({
          where: { clientId: id },
        });

        await tx.client.delete({
          where: { id },
        });
      });

      await this.userActivityService.logActivity({
        userId: loggedUser.id,
        action: 'DELETE',
        objectType: 'CLIENT',
        objectId: String(client.id),
        metadata: {
          clientId: client.id,
          clientName: client.name,
        },
      });

      return new BaseResponse({
        data: null,
        message: 'Client and associated jobs deleted successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }
}
