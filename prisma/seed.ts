import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function main() {
  // const super_admin = await prisma.user.create({
  //   data: {
  //     id: 'e0e02123-e0aa-4edb-b1dd-1aa7345cc5a4',
  //     email: '<EMAIL>',
  //     password: '$2b$10$n8TXSNoNFqYBKhUQWRF1EeyW7nTbKEkpJpAWPcBlfNs.TwsqkW3ye',
  //     firstName: 'Talent',
  //     lastName: 'Labs',
  //     role: 'SUPER_ADMIN',
  //   },
  // });

  // const admin = await prisma.user.create({
  //   data: {
  //     id: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
  //     email: '<EMAIL>',
  //     password: '$2b$10$YWFoBp0WOTqbLKbEgG8rCua5gcawnV/jXYb1..LuQ05T1ZpEkp6.e',
  //     firstName: 'Bob',
  //     lastName: 'Johnson',
  //     role: 'ADMIN',
  //   },
  // });

  // const team = await prisma.user.create({
  //   data: {
  //     id: 'e0e02123-9xhc-4edb-b1dd-1761345cc5a4',
  //     email: '<EMAIL>',
  //     password: '$2b$10$YWFoBp0WOTqbLKbEgG8rCua5gcawnV/jXYb1..LuQ05T1ZpEkp6.e',
  //     firstName: 'Carol',
  //     lastName: 'Williams',
  //     role: 'TEAM',
  //   },
  // });

  const client1 = await prisma.client.create({
    data: {
      name: 'Rodriguez, Figueroa and Sanchez',
      email: '<EMAIL>',
      industry: 'Enhance',
      phone: '************',
      location: 'Ericmouth',
      summary: `Here grow gas enough. Role movie win.`,
      website: 'http://www.daniel.net/',
      status: 'PROSPECT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'English as a foreign language teacher',
      description: `Director allow firm environment. Tree note responsibility defense material. Central cause seat much section investment on.`,
      client: { connect: { id: client1.id } },
      department: 'High',
      location: 'East William',
      type: 'FULL_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['benefit', 'someone', 'anyone'],
      responsibilities: [
        'Check civil quite others.',
        'High you more wife team activity.',
      ],
      requirements: [
        'Race Mr environment political.',
        'Fall citizen about reveal.',
      ],
      benefits: [
        'Will seven medical blood personal.',
        'Participant check several much single morning a.',
      ],
      salaryMin: 6037,
      salaryMax: 8163,
      salaryCurrency: 'CAD',
      salaryPeriod: 'HOURLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Jewellery designer',
      description: `Born guy world. Dream drive note bad rule. Within mouth call process.`,
      client: { connect: { id: client1.id } },
      department: 'Your',
      location: 'West Allison',
      type: 'FULL_TIME',
      workLocation: 'REMOTE',
      keySkills: ['policy', 'land', 'development'],
      responsibilities: [
        'Sense ready require human public health tonight.',
        'Building different full open discover detail audience.',
      ],
      requirements: [
        'Arrive attack all form method everything magazine much.',
        'Office drug list imagine behind probably great.',
      ],
      benefits: [
        'Level rock pull worker better I.',
        'Song body court movie cell contain.',
      ],
      salaryMin: 6033,
      salaryMax: 10687,
      salaryCurrency: 'EUR',
      salaryPeriod: 'YEARLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Psychologist, counselling',
      description: `Kitchen technology nearly anything. Structure why unit support coach magazine degree.`,
      client: { connect: { id: client1.id } },
      department: 'Institution',
      location: 'East Nicholasfurt',
      type: 'FULL_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['evidence', 'with', 'step'],
      responsibilities: [
        'Expect recent room situation product main couple.',
        'Why often my security arm.',
      ],
      requirements: [
        'Live try most arm meet surface attention attack.',
        'Identify walk now often always.',
      ],
      benefits: [
        'Information on mission various.',
        'Prove fire enter capital population.',
      ],
      salaryMin: 3383,
      salaryMax: 5278,
      salaryCurrency: 'CAD',
      salaryPeriod: 'YEARLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Programmer, systems',
      description: `Act perform none beyond. Defense enter value thing these hard citizen. Region particularly would pressure account stage federal.`,
      client: { connect: { id: client1.id } },
      department: 'Record',
      location: 'East Josephville',
      type: 'INTERNSHIP',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['situation', 'on', 'clearly'],
      responsibilities: [
        'Example decision garden reach table.',
        'Major town suffer begin interest everybody about.',
      ],
      requirements: [
        'Pm energy scientist necessary.',
        'Night born war real chance along.',
      ],
      benefits: [
        'Old challenge camera final together someone.',
        'Together decide economic.',
      ],
      salaryMin: 5932,
      salaryMax: 9593,
      salaryCurrency: 'CNY',
      salaryPeriod: 'HOURLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Civil engineer, contracting',
      description: `This image per choice upon ten wish. Thing agent say forward. Soon ten specific environment skin blue.`,
      client: { connect: { id: client1.id } },
      department: 'Turn',
      location: 'Port Thomasborough',
      type: 'INTERNSHIP',
      workLocation: 'HYBRID',
      keySkills: ['technology', 'why', 'magazine'],
      responsibilities: [
        'Figure somebody dinner age cover foreign.',
        'Whom evidence political hundred.',
      ],
      requirements: [
        'Former agree theory end oil worker although.',
        'Its rock finish paper memory history office effort.',
      ],
      benefits: [
        'Ability understand Mrs rest score provide.',
        'Issue we TV perhaps professional by.',
      ],
      salaryMin: 6315,
      salaryMax: 10875,
      salaryCurrency: 'USD',
      salaryPeriod: 'HOURLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Hydrologist',
      description: `Project for recent never court. Quality total past feeling nature a. Decision size parent focus kid.`,
      client: { connect: { id: client1.id } },
      department: 'Republican',
      location: 'New Thomas',
      type: 'CONTRACT',
      workLocation: 'HYBRID',
      keySkills: ['wear', 'style', 'simply'],
      responsibilities: [
        'Probably exist professional people.',
        'Stand seem pull different.',
      ],
      requirements: [
        'Challenge animal worker particularly shoulder.',
        'Increase picture create recent manager during prevent.',
      ],
      benefits: [
        'Throughout team those.',
        'Despite sound receive let newspaper true.',
      ],
      salaryMin: 3636,
      salaryMax: 5517,
      salaryCurrency: 'JPY',
      salaryPeriod: 'HOURLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Community development worker',
      description: `Soon property write other. Partner rest measure store natural today. Late seem style everyone sing machine dream.`,
      client: { connect: { id: client1.id } },
      department: 'Less',
      location: 'Lake Justinview',
      type: 'TEMPORARY',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['government', 'front', 'walk'],
      responsibilities: [
        'Ball character him check degree increase former.',
        'Indeed deal information toward once receive task.',
      ],
      requirements: [
        'Herself accept goal send table well industry.',
        'Son today major event magazine home protect.',
      ],
      benefits: [
        'Right subject try wonder move trade.',
        'North agree poor career left anyone here deep.',
      ],
      salaryMin: 4470,
      salaryMax: 8941,
      salaryCurrency: 'JPY',
      salaryPeriod: 'YEARLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Journalist, newspaper',
      description: `Everything week instead strong like see. Yourself glass practice. Senior wear current.`,
      client: { connect: { id: client1.id } },
      department: 'Again',
      location: 'East David',
      type: 'FULL_TIME',
      workLocation: 'REMOTE',
      keySkills: ['relationship', 'building', 'organization'],
      responsibilities: [
        'Let eight hard paper white.',
        'Sing clearly find official.',
      ],
      requirements: [
        'Office traditional heart walk cup.',
        'Real physical big significant sure outside building worker.',
      ],
      benefits: [
        'Girl into have.',
        'Positive actually information majority item gun through.',
      ],
      salaryMin: 4881,
      salaryMax: 8077,
      salaryCurrency: 'EUR',
      salaryPeriod: 'MONTHLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Psychiatric nurse',
      description: `Computer miss practice pattern try simple. Itself room environmental system store beautiful think.`,
      client: { connect: { id: client1.id } },
      department: 'Either',
      location: 'Hudsonfurt',
      type: 'INTERNSHIP',
      workLocation: 'HYBRID',
      keySkills: ['great', 'air', 'miss'],
      responsibilities: [
        'Consider deep something future they red.',
        'New might course drop image.',
      ],
      requirements: [
        'Serve real position make society behavior develop.',
        'Fill ok list most international.',
      ],
      benefits: [
        'Former reflect even edge building court build.',
        'Week real course school everybody operation set.',
      ],
      salaryMin: 6397,
      salaryMax: 9971,
      salaryCurrency: 'JPY',
      salaryPeriod: 'YEARLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Therapist, speech and language',
      description: `Strategy fast guess few. Call animal approach factor want point sell.`,
      client: { connect: { id: client1.id } },
      department: 'Board',
      location: 'New Justinfort',
      type: 'FULL_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['let', 'grow', 'create'],
      responsibilities: [
        'Education technology box.',
        'Husband available picture approach.',
      ],
      requirements: [
        'Specific we be easy.',
        'Share paper so difficult mission late kind.',
      ],
      benefits: [
        'Wrong figure perform participant science way debate decision.',
        'Church community avoid able.',
      ],
      salaryMin: 5708,
      salaryMax: 7641,
      salaryCurrency: 'AUD',
      salaryPeriod: 'HOURLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  const client2 = await prisma.client.create({
    data: {
      name: 'Norris-Harrison',
      email: '<EMAIL>',
      industry: 'Generate',
      phone: '************',
      location: 'Scottmouth',
      summary: `Wall matter management ball always it. Page ago director purpose team onto.`,
      website: 'https://www.graham.info/',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Magazine features editor',
      description: `Fly bit claim in many production. Spend nearly lawyer fire follow wife. Ten stay ability thank left approach.`,
      client: { connect: { id: client2.id } },
      department: 'National',
      location: 'Cindyfort',
      type: 'TEMPORARY',
      workLocation: 'HYBRID',
      keySkills: ['process', 'letter', 'soon'],
      responsibilities: [
        'Data near until.',
        'Thing machine ahead picture son report.',
      ],
      requirements: [
        'Nearly need behavior yeah tree.',
        'Water positive child usually factor relate indeed.',
      ],
      benefits: [
        'Woman during necessary himself two meet these.',
        'Everybody so increase various.',
      ],
      salaryMin: 4857,
      salaryMax: 8460,
      salaryCurrency: 'JPY',
      salaryPeriod: 'HOURLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Runner, broadcasting/film/video',
      description: `Executive fear only. Tend land machine forward several help usually.`,
      client: { connect: { id: client2.id } },
      department: 'True',
      location: 'Lake John',
      type: 'CONTRACT',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['identify', 'east', 'total'],
      responsibilities: [
        'Analysis seat relate specific history professional.',
        'Manager already maybe opportunity.',
      ],
      requirements: ['Them key moment lead.', 'Improve pressure child light.'],
      benefits: [
        'Full realize power system system.',
        'Here first responsibility service their along attention.',
      ],
      salaryMin: 5745,
      salaryMax: 7838,
      salaryCurrency: 'EUR',
      salaryPeriod: 'YEARLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Holiday representative',
      description: `Its better plant their. Coach federal ahead food argue grow.`,
      client: { connect: { id: client2.id } },
      department: 'Lawyer',
      location: 'East Amanda',
      type: 'INTERNSHIP',
      workLocation: 'REMOTE',
      keySkills: ['others', 'rest', 'structure'],
      responsibilities: [
        'Color bad that people.',
        'Marriage on discussion point least.',
      ],
      requirements: [
        'Together let explain.',
        'Citizen kid generation onto police interesting economic.',
      ],
      benefits: [
        'Current his low down occur.',
        'Fast recognize against stop how account ten.',
      ],
      salaryMin: 4002,
      salaryMax: 5671,
      salaryCurrency: 'INR',
      salaryPeriod: 'MONTHLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Artist',
      description: `Strategy total simply discover soon despite couple. Question return process stuff pick. Position final kid often run bed far section. Customer skill theory hand.`,
      client: { connect: { id: client2.id } },
      department: 'More',
      location: 'Mariaview',
      type: 'INTERNSHIP',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['white', 'thank', 'yet'],
      responsibilities: [
        'Whose far that sound life away senior.',
        'Team right woman whose source.',
      ],
      requirements: [
        'Bed house enough buy happy see.',
        'Once state wait board doctor.',
      ],
      benefits: [
        'People wall foreign.',
        'Determine as statement travel few impact cause watch.',
      ],
      salaryMin: 5804,
      salaryMax: 8132,
      salaryCurrency: 'USD',
      salaryPeriod: 'HOURLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Industrial buyer',
      description: `Best thought career law. Industry score choice increase between majority impact. Allow have kitchen wear talk.`,
      client: { connect: { id: client2.id } },
      department: 'Bit',
      location: 'Amystad',
      type: 'CONTRACT',
      workLocation: 'HYBRID',
      keySkills: ['ask', 'information', 'fact'],
      responsibilities: [
        'Why we station begin deep.',
        'Wife anything four writer skin day stop never.',
      ],
      requirements: [
        'Democratic thank forget challenge too able teach certain.',
        'Music sometimes body term.',
      ],
      benefits: ['Address so draw food.', 'Enter camera inside box.'],
      salaryMin: 4096,
      salaryMax: 5367,
      salaryCurrency: 'CAD',
      salaryPeriod: 'YEARLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Bonds trader',
      description: `Sing message board mean war analysis situation. Feeling poor all your suggest international blue. Available ball part assume every plan.`,
      client: { connect: { id: client2.id } },
      department: 'Official',
      location: 'Oscarhaven',
      type: 'PART_TIME',
      workLocation: 'REMOTE',
      keySkills: ['financial', 'long', 'mouth'],
      responsibilities: [
        'Involve soldier boy pressure there necessary tend involve.',
        'Including still human role fine.',
      ],
      requirements: [
        'Professor hear check type attack story.',
        'It season head candidate.',
      ],
      benefits: [
        'Only no form business.',
        'Them wait institution trouble anything explain.',
      ],
      salaryMin: 5044,
      salaryMax: 7664,
      salaryCurrency: 'INR',
      salaryPeriod: 'HOURLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Insurance underwriter',
      description: `Official defense prevent difference. Glass news boy everything. Southern suddenly window stand party.`,
      client: { connect: { id: client2.id } },
      department: 'Question',
      location: 'New Sarahview',
      type: 'PART_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['air', 'couple', 'explain'],
      responsibilities: [
        'Tv go consider century price.',
        'Center plan air second.',
      ],
      requirements: [
        'Heavy law church find food.',
        'Other third choose senior anyone bank kitchen.',
      ],
      benefits: [
        'Magazine kind event sense box involve.',
        'Question or money final determine.',
      ],
      salaryMin: 6051,
      salaryMax: 9350,
      salaryCurrency: 'AUD',
      salaryPeriod: 'YEARLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Tour manager',
      description: `Daughter war should. Face build market issue can.`,
      client: { connect: { id: client2.id } },
      department: 'No',
      location: 'New Angelica',
      type: 'INTERNSHIP',
      workLocation: 'HYBRID',
      keySkills: ['relate', 'sure', 'health'],
      responsibilities: [
        'However score job least.',
        'Television office of remember.',
      ],
      requirements: [
        'Face if whom commercial way least.',
        'Because such during open model how.',
      ],
      benefits: [
        'Second develop single baby plan.',
        'Member town glass road standard spring door.',
      ],
      salaryMin: 4482,
      salaryMax: 6380,
      salaryCurrency: 'GBP',
      salaryPeriod: 'YEARLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Dancer',
      description: `Community check service away week. Management senior service large under north play person.`,
      client: { connect: { id: client2.id } },
      department: 'Treat',
      location: 'Edwardland',
      type: 'FULL_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['manager', 'letter', 'program'],
      responsibilities: ['Someone option goal avoid left.', 'Arm story baby.'],
      requirements: ['Past medical leg never.', 'Last special prepare.'],
      benefits: [
        'Painting may whatever late specific study.',
        'Mean common easy just.',
      ],
      salaryMin: 6527,
      salaryMax: 7976,
      salaryCurrency: 'GBP',
      salaryPeriod: 'YEARLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Print production planner',
      description: `Particular hair truth hold simple quickly appear. Positive at behind question exist rich prevent trade. Four old center glass.`,
      client: { connect: { id: client2.id } },
      department: 'Reality',
      location: 'Andersonland',
      type: 'TEMPORARY',
      workLocation: 'REMOTE',
      keySkills: ['force', 'operation', 'however'],
      responsibilities: [
        'Spend prove stock school rate money.',
        'Meet adult final week game she.',
      ],
      requirements: [
        'Difficult case baby such big.',
        'Strong this also from short capital heavy.',
      ],
      benefits: [
        'Congress realize person total.',
        'Analysis hair rest wide particular sell.',
      ],
      salaryMin: 3260,
      salaryMax: 5836,
      salaryCurrency: 'CNY',
      salaryPeriod: 'YEARLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  const client3 = await prisma.client.create({
    data: {
      name: 'Young-Allen',
      email: '<EMAIL>',
      industry: 'Target',
      phone: '862.392.4075x81814',
      location: 'Port Ethanmouth',
      summary: `Store begin treat stage. Us increase how hear history bank. Agreement us stuff practice social.`,
      website: 'http://conley.org/',
      status: 'PROSPECT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Secretary, company',
      description: `Simply down business draw. Lead book toward others administration middle drop century. Ability good number cost property model.`,
      client: { connect: { id: client3.id } },
      department: 'Herself',
      location: 'New Amyhaven',
      type: 'CONTRACT',
      workLocation: 'REMOTE',
      keySkills: ['once', 'again', 'in'],
      responsibilities: [
        'Analysis hit health section ground attack drop.',
        'Billion old series card good full poor store.',
      ],
      requirements: [
        'Wonder long consider care respond want already.',
        'Analysis current among value middle.',
      ],
      benefits: [
        'List already positive experience television answer pretty.',
        'Buy happy threat sea thus.',
      ],
      salaryMin: 6525,
      salaryMax: 11385,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEARLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Nature conservation officer',
      description: `Many marriage under mind. Risk bad own state. Family bill foreign fast knowledge response coach.`,
      client: { connect: { id: client3.id } },
      department: 'Life',
      location: 'Sergiostad',
      type: 'INTERNSHIP',
      workLocation: 'HYBRID',
      keySkills: ['possible', 'learn', 'face'],
      responsibilities: ['Avoid point guy song.', 'Well central parent sit.'],
      requirements: [
        'Call door population.',
        'Him note painting quickly we head.',
      ],
      benefits: [
        'Off morning huge power.',
        'Whether ago control military trial.',
      ],
      salaryMin: 6148,
      salaryMax: 9773,
      salaryCurrency: 'JPY',
      salaryPeriod: 'HOURLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Commercial/residential surveyor',
      description: `Region Democrat partner really your. But table later together knowledge.`,
      client: { connect: { id: client3.id } },
      department: 'Begin',
      location: 'Elizabethland',
      type: 'TEMPORARY',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['oil', 'occur', 'fill'],
      responsibilities: [
        'Surface Republican until point soldier.',
        'Difference world society door management guess occur.',
      ],
      requirements: [
        'Sound necessary partner beat finally yourself rest.',
        'Improve important offer by first avoid letter.',
      ],
      benefits: [
        'General there sister policy consider whom item.',
        'Story million fight class.',
      ],
      salaryMin: 4858,
      salaryMax: 5871,
      salaryCurrency: 'AUD',
      salaryPeriod: 'YEARLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Equality and diversity officer',
      description: `Thus suffer economy play nearly by field. Behavior political option oil commercial person top. Successful nor stay agreement animal political.`,
      client: { connect: { id: client3.id } },
      department: 'Part',
      location: 'Debrashire',
      type: 'INTERNSHIP',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['keep', 'prepare', 'purpose'],
      responsibilities: [
        'Marriage phone face.',
        'Lot source rate father authority.',
      ],
      requirements: [
        'Citizen indeed less future.',
        'Fine responsibility safe team.',
      ],
      benefits: [
        'Candidate have no five letter environment.',
        'Cell anything war ten industry.',
      ],
      salaryMin: 6565,
      salaryMax: 10126,
      salaryCurrency: 'AUD',
      salaryPeriod: 'YEARLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Designer, jewellery',
      description: `Return couple city you level these market. War measure whom Democrat. Any example our successful pull experience.`,
      client: { connect: { id: client3.id } },
      department: 'Across',
      location: 'South Dennisview',
      type: 'PART_TIME',
      workLocation: 'HYBRID',
      keySkills: ['short', 'history', 'south'],
      responsibilities: [
        'Hand hundred now crime network available.',
        'Late near stay perhaps particularly campaign benefit.',
      ],
      requirements: [
        'Some fund voice sense current meeting.',
        'American although require sound mind chance throw.',
      ],
      benefits: [
        'Foreign party class wrong.',
        'Order medical meeting majority none.',
      ],
      salaryMin: 6123,
      salaryMax: 7784,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEARLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Public relations officer',
      description: `Former body indicate film together the.`,
      client: { connect: { id: client3.id } },
      department: 'Professional',
      location: 'New Thomasshire',
      type: 'TEMPORARY',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['participant', 'drive', 'set'],
      responsibilities: [
        'On color pick.',
        'Simple herself start drug occur red course kid.',
      ],
      requirements: [
        'Charge real improve simple turn their.',
        'Relationship million night your.',
      ],
      benefits: [
        'Moment finish community treatment garden great sign.',
        'Particular court east newspaper different.',
      ],
      salaryMin: 3458,
      salaryMax: 8263,
      salaryCurrency: 'JPY',
      salaryPeriod: 'MONTHLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Human resources officer',
      description: `Real major look night various. Explain of myself time house.`,
      client: { connect: { id: client3.id } },
      department: 'Hair',
      location: 'West Bobby',
      type: 'FULL_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['send', 'bar', 'half'],
      responsibilities: [
        'Pretty section degree still even no.',
        'Case past only drug prove.',
      ],
      requirements: [
        'Difficult do beyond form line race case.',
        'Within citizen present thing little practice sense.',
      ],
      benefits: [
        'Fly position traditional become off.',
        'School sure also TV individual study value.',
      ],
      salaryMin: 6596,
      salaryMax: 9919,
      salaryCurrency: 'EUR',
      salaryPeriod: 'HOURLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Animator',
      description: `Control see the face also fear owner. Expect writer myself management voice surface. Evening speak former room possible responsibility add.`,
      client: { connect: { id: client3.id } },
      department: 'Green',
      location: 'Ashleyborough',
      type: 'FULL_TIME',
      workLocation: 'REMOTE',
      keySkills: ['continue', 'popular', 'total'],
      responsibilities: [
        'Still middle beautiful protect continue cell.',
        'Very according himself land environment form.',
      ],
      requirements: [
        'Reveal activity president realize artist brother fill if.',
        'Type thousand show real police wait happen.',
      ],
      benefits: [
        'Despite card check security paper.',
        'Reduce tree serious soon stay seven.',
      ],
      salaryMin: 6136,
      salaryMax: 7651,
      salaryCurrency: 'GBP',
      salaryPeriod: 'YEARLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Therapist, occupational',
      description: `Land region back nor article natural measure. Mr clearly take kind quite response major.`,
      client: { connect: { id: client3.id } },
      department: 'Way',
      location: 'Stokesmouth',
      type: 'INTERNSHIP',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['cause', 'must', 'just'],
      responsibilities: [
        'Nor next pull final against effort able.',
        'Official and general as yes.',
      ],
      requirements: [
        'Worry line expert conference.',
        'She within position inside.',
      ],
      benefits: [
        'Large true help bag who themselves.',
        'President girl condition Mr.',
      ],
      salaryMin: 4085,
      salaryMax: 7246,
      salaryCurrency: 'CNY',
      salaryPeriod: 'HOURLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Seismic interpreter',
      description: `Strong series without leg rest. Mother statement easy get every visit.`,
      client: { connect: { id: client3.id } },
      department: 'Share',
      location: 'North Lisachester',
      type: 'CONTRACT',
      workLocation: 'HYBRID',
      keySkills: ['possible', 'space', 'would'],
      responsibilities: [
        'Drive attack order.',
        'Our reflect any scientist I doctor describe.',
      ],
      requirements: [
        'Cell year doctor trouble.',
        'Five our pull fly few century produce.',
      ],
      benefits: [
        'Including every news option same.',
        'Personal interview many win.',
      ],
      salaryMin: 5751,
      salaryMax: 9412,
      salaryCurrency: 'JPY',
      salaryPeriod: 'MONTHLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  const client4 = await prisma.client.create({
    data: {
      name: 'Powers, Floyd and Flores',
      email: '<EMAIL>',
      industry: 'Iterate',
      phone: '001-270-866-8826x343',
      location: 'Deborahmouth',
      summary: `Ten scientist administration network once result far.`,
      website: 'https://www.mooney.com/',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Art therapist',
      description: `Case piece attack character despite lay.`,
      client: { connect: { id: client4.id } },
      department: 'As',
      location: 'Hopkinstown',
      type: 'PART_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['according', 'night', 'ok'],
      responsibilities: [
        'Activity morning ever within admit spend.',
        'South travel blood benefit chance court.',
      ],
      requirements: [
        'Education great some bad where learn during.',
        'Feel stock ball yard practice.',
      ],
      benefits: ['Behavior here need.', 'Pm reflect bar suddenly.'],
      salaryMin: 3262,
      salaryMax: 5646,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEARLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Learning disability nurse',
      description: `Thus try discover top. Little another avoid understand tonight nor allow. Administration debate certain star start up.`,
      client: { connect: { id: client4.id } },
      department: 'Firm',
      location: 'New John',
      type: 'INTERNSHIP',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['they', 'time', 'improve'],
      responsibilities: [
        'Forward sense cause write right may window.',
        'Best discussion away.',
      ],
      requirements: [
        'Shoulder western however similar ahead event yeah make.',
        'Need heart control one herself sign.',
      ],
      benefits: [
        'Something require bank child Republican.',
        'Science first blood accept.',
      ],
      salaryMin: 3029,
      salaryMax: 4319,
      salaryCurrency: 'USD',
      salaryPeriod: 'HOURLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Insurance account manager',
      description: `Seven bill beautiful issue news mention. Ago government behavior our. Tell activity including single right nice.`,
      client: { connect: { id: client4.id } },
      department: 'Eight',
      location: 'Lake Oscar',
      type: 'FULL_TIME',
      workLocation: 'HYBRID',
      keySkills: ['chair', 'exist', 'fish'],
      responsibilities: [
        'Manage as itself inside machine.',
        'Guess want result many.',
      ],
      requirements: [
        'Crime blood fight we forward per sound forget.',
        'Issue statement prepare organization feel owner.',
      ],
      benefits: [
        'Look concern huge five same.',
        'Whose site for exactly skill half usually.',
      ],
      salaryMin: 3290,
      salaryMax: 6395,
      salaryCurrency: 'CAD',
      salaryPeriod: 'MONTHLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Engineer, communications',
      description: `Conference should agree road wall decide something. Standard somebody important material. Drug probably resource smile available. Look husband media turn reality myself so growth.`,
      client: { connect: { id: client4.id } },
      department: 'Want',
      location: 'North Kaylastad',
      type: 'PART_TIME',
      workLocation: 'REMOTE',
      keySkills: ['compare', 'radio', 'position'],
      responsibilities: [
        'None population position.',
        'Whole involve action else member million.',
      ],
      requirements: [
        'Reach determine hear leg quickly real character young.',
        'Bit indeed which break wait center find.',
      ],
      benefits: [
        'Six miss give best reveal laugh attack.',
        'Special boy support.',
      ],
      salaryMin: 3541,
      salaryMax: 7503,
      salaryCurrency: 'INR',
      salaryPeriod: 'HOURLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Clinical embryologist',
      description: `Might necessary former interview left. Picture control price how scene third.`,
      client: { connect: { id: client4.id } },
      department: 'Listen',
      location: 'Priceborough',
      type: 'TEMPORARY',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['describe', 'family', 'soldier'],
      responsibilities: [
        'Officer son Mr hand.',
        'Leader energy television month police.',
      ],
      requirements: [
        'Home hotel box goal leader common purpose song.',
        'Opportunity public finish draw bring health center home.',
      ],
      benefits: [
        'Case himself control player really population yourself never.',
        'Eye cost success people tend weight machine.',
      ],
      salaryMin: 3386,
      salaryMax: 4783,
      salaryCurrency: 'CNY',
      salaryPeriod: 'MONTHLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Engineer, broadcasting (operations)',
      description: `Measure manager range indeed style major bar. Somebody determine allow. Mrs collection leader score she.`,
      client: { connect: { id: client4.id } },
      department: 'Often',
      location: 'New Dannyside',
      type: 'TEMPORARY',
      workLocation: 'HYBRID',
      keySkills: ['than', 'hit', 'occur'],
      responsibilities: [
        'Service kid despite.',
        'Bill stop seem fear yourself last.',
      ],
      requirements: [
        'Television beautiful tend bring speech decide.',
        'Ready technology particular into pattern size spend south.',
      ],
      benefits: [
        'Go exactly much food region eye environment.',
        'World quickly believe while size try yeah.',
      ],
      salaryMin: 6538,
      salaryMax: 10524,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEARLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Computer games developer',
      description: `Chair focus easy such hotel. Officer environmental increase later.`,
      client: { connect: { id: client4.id } },
      department: 'Property',
      location: 'South Zacharyview',
      type: 'FULL_TIME',
      workLocation: 'HYBRID',
      keySkills: ['run', 'would', 'understand'],
      responsibilities: [
        'Lawyer maintain old than suggest behavior.',
        'Play recently sure somebody huge why station.',
      ],
      requirements: [
        'Environmental stock front official not magazine gas.',
        'Animal phone student.',
      ],
      benefits: ['Buy tax kid.', 'Yeah into yet.'],
      salaryMin: 5982,
      salaryMax: 8371,
      salaryCurrency: 'EUR',
      salaryPeriod: 'HOURLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Consulting civil engineer',
      description: `Themselves theory behavior growth. Head tough close how.`,
      client: { connect: { id: client4.id } },
      department: 'Focus',
      location: 'South Diana',
      type: 'PART_TIME',
      workLocation: 'REMOTE',
      keySkills: ['score', 'beyond', 'ability'],
      responsibilities: [
        'Live car difficult quite act.',
        'Stage write institution car audience him.',
      ],
      requirements: [
        'Billion happen federal him.',
        'Political quality attention none.',
      ],
      benefits: [
        'Practice key reveal physical character in rather team.',
        'To cause but role.',
      ],
      salaryMin: 4837,
      salaryMax: 6411,
      salaryCurrency: 'CNY',
      salaryPeriod: 'HOURLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Make',
      description: `Social easy popular easy over return. Single real summer probably feeling. Parent country hot position six soldier dream.`,
      client: { connect: { id: client4.id } },
      department: 'Imagine',
      location: 'East Victorland',
      type: 'TEMPORARY',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['news', 'visit', 'actually'],
      responsibilities: [
        'Value car team nothing half raise.',
        'West side admit attack energy always art.',
      ],
      requirements: [
        'Media pick may indeed reduce bar.',
        'Mrs check base bad TV window choice.',
      ],
      benefits: [
        'Hospital plant strong race at recognize.',
        'Around beat its law participant.',
      ],
      salaryMin: 6581,
      salaryMax: 11362,
      salaryCurrency: 'EUR',
      salaryPeriod: 'MONTHLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Insurance broker',
      description: `Actually case activity market. Mission situation result cold camera sit.`,
      client: { connect: { id: client4.id } },
      department: 'Exist',
      location: 'Port Rachelmouth',
      type: 'FULL_TIME',
      workLocation: 'REMOTE',
      keySkills: ['surface', 'laugh', 'expert'],
      responsibilities: [
        'Ask Republican office baby lawyer growth matter.',
        'Cold church significant thus purpose boy scientist.',
      ],
      requirements: [
        'Analysis television each.',
        'Hair turn condition whether along sort research.',
      ],
      benefits: [
        'Different eat trouble floor step number.',
        'Maybe top conference source.',
      ],
      salaryMin: 5214,
      salaryMax: 9638,
      salaryCurrency: 'USD',
      salaryPeriod: 'HOURLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  const client5 = await prisma.client.create({
    data: {
      name: 'Finley Inc',
      email: '<EMAIL>',
      industry: 'Disintermediate',
      phone: '001-************',
      location: 'North Sarah',
      summary: `Must player really act friend.`,
      website: 'https://www.bradley.com/',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Horticulturist, amenity',
      description: `Unit hotel story for laugh do. Carry show board black last.`,
      client: { connect: { id: client5.id } },
      department: 'Former',
      location: 'Martinezstad',
      type: 'TEMPORARY',
      workLocation: 'HYBRID',
      keySkills: ['speech', 'despite', 'live'],
      responsibilities: [
        'Leg system bed space fight.',
        'American structure foreign before eat green message quality.',
      ],
      requirements: [
        'Star community weight take new.',
        'Economic left sound cause activity store work.',
      ],
      benefits: ['Cup hundred rather visit.', 'Old else spend against.'],
      salaryMin: 4971,
      salaryMax: 6846,
      salaryCurrency: 'CNY',
      salaryPeriod: 'HOURLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Bookseller',
      description: `Speak begin suggest speak business walk. Collection off start.`,
      client: { connect: { id: client5.id } },
      department: 'Indicate',
      location: 'Johnsonchester',
      type: 'TEMPORARY',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['consider', 'health', 'difficult'],
      responsibilities: [
        'Every land chance pass.',
        'Early far include nearly article evidence case current.',
      ],
      requirements: [
        'Eat yes myself affect him require look.',
        'General not focus establish ago others ahead.',
      ],
      benefits: [
        'Around yard morning short yourself.',
        'Beyond prevent entire staff.',
      ],
      salaryMin: 4599,
      salaryMax: 6685,
      salaryCurrency: 'INR',
      salaryPeriod: 'MONTHLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Banker',
      description: `Suffer something great stuff suddenly compare or. Trial agreement red way nor none.`,
      client: { connect: { id: client5.id } },
      department: 'Cup',
      location: 'Weaverburgh',
      type: 'INTERNSHIP',
      workLocation: 'REMOTE',
      keySkills: ['military', 'always', 'believe'],
      responsibilities: [
        'Discussion despite money a.',
        'Cut candidate response try such food against only.',
      ],
      requirements: [
        'Similar suffer team whether.',
        'Relate product only from follow wish.',
      ],
      benefits: [
        'Social whether power company why really.',
        'Skin development open compare fill read camera rock.',
      ],
      salaryMin: 5942,
      salaryMax: 8935,
      salaryCurrency: 'GBP',
      salaryPeriod: 'HOURLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Teacher, primary school',
      description: `Allow own TV whose determine not view. Former back get floor. Start prove role from.`,
      client: { connect: { id: client5.id } },
      department: 'Activity',
      location: 'Jasonland',
      type: 'PART_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['hard', 'establish', 'stop'],
      responsibilities: [
        'Result answer just information.',
        'This manage her national.',
      ],
      requirements: [
        'Stock small official serve.',
        'Class available suffer far five.',
      ],
      benefits: [
        'Avoid color heart rule.',
        'Walk within full world throw relate issue.',
      ],
      salaryMin: 5372,
      salaryMax: 9385,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEARLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Development worker, international aid',
      description: `Church big day couple recent reveal. Enter example down anyone occur style child. About because station person lose best. Color factor bank such final.`,
      client: { connect: { id: client5.id } },
      department: 'Information',
      location: 'Burnsview',
      type: 'FULL_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['benefit', 'quality', 'off'],
      responsibilities: [
        'Continue those hard knowledge enjoy let.',
        'Student nor character recent benefit.',
      ],
      requirements: [
        'Space including series dinner article hit.',
        'Industry movement term little think live bad.',
      ],
      benefits: [
        'Maybe too song.',
        'Challenge we last we cold deep cover amount.',
      ],
      salaryMin: 5392,
      salaryMax: 8344,
      salaryCurrency: 'GBP',
      salaryPeriod: 'HOURLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Careers information officer',
      description: `Young physical street election treat. Beautiful news need task. Leader them skill performance mission information increase.`,
      client: { connect: { id: client5.id } },
      department: 'Choose',
      location: 'North Annaville',
      type: 'PART_TIME',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['stock', 'feeling', 'trip'],
      responsibilities: [
        'They there enough pressure occur list common.',
        'Century participant really although threat former.',
      ],
      requirements: [
        'Any little environmental head full.',
        'Financial successful teach range win.',
      ],
      benefits: [
        'Care wind kitchen stay especially.',
        'Painting become kid tree imagine machine.',
      ],
      salaryMin: 5437,
      salaryMax: 6715,
      salaryCurrency: 'CAD',
      salaryPeriod: 'MONTHLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Scientist, clinical (histocompatibility and immunogenetics)',
      description: `Middle city find medical. Sister modern notice community. Student economic lead truth relate.`,
      client: { connect: { id: client5.id } },
      department: 'Eye',
      location: 'Madisonhaven',
      type: 'INTERNSHIP',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['industry', 'prove', 'wind'],
      responsibilities: [
        'Race drop major land.',
        'Only glass clear thus see read expect.',
      ],
      requirements: [
        'Anyone take sister Democrat determine different.',
        'Especially important after sister should.',
      ],
      benefits: ['Listen car community.', 'Difference part wall.'],
      salaryMin: 5371,
      salaryMax: 8806,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEARLY',
      status: 'DRAFT',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Haematologist',
      description: `Surface learn design few minute perhaps. Bill memory production successful hear positive participant fear. Loss from everybody foot.`,
      client: { connect: { id: client5.id } },
      department: 'Factor',
      location: 'Dianaport',
      type: 'TEMPORARY',
      workLocation: 'REMOTE',
      keySkills: ['cup', 'citizen', 'nation'],
      responsibilities: [
        'Tonight south sort outside produce.',
        'Return pretty young else.',
      ],
      requirements: [
        'Identify say on yes staff gas.',
        'Rather point little wait grow.',
      ],
      benefits: [
        'Suddenly subject theory Congress best.',
        'Scientist rise next tree.',
      ],
      salaryMin: 5390,
      salaryMax: 8705,
      salaryCurrency: 'JPY',
      salaryPeriod: 'MONTHLY',
      status: 'ACTIVE',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Legal executive',
      description: `Great season film small former. One less brother approach plant.`,
      client: { connect: { id: client5.id } },
      department: 'Partner',
      location: 'South Pamelahaven',
      type: 'CONTRACT',
      workLocation: 'FULLY_ONSITE',
      keySkills: ['call', 'trip', 'responsibility'],
      responsibilities: [
        'Morning including that character yeah garden.',
        'Throughout would century television Congress ball forward.',
      ],
      requirements: [
        'Finish around later decide.',
        'Office citizen size sister.',
      ],
      benefits: [
        'Pass plant idea whole.',
        'Above move room wear throughout series.',
      ],
      salaryMin: 4087,
      salaryMax: 6708,
      salaryCurrency: 'GBP',
      salaryPeriod: 'YEARLY',
      status: 'PAUSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  await prisma.job.create({
    data: {
      title: 'Set designer',
      description: `Quality phone eat. Fine bill me fill assume animal.`,
      client: { connect: { id: client5.id } },
      department: 'Fill',
      location: 'Lunahaven',
      type: 'TEMPORARY',
      workLocation: 'HYBRID',
      keySkills: ['green', 'leader', 'view'],
      responsibilities: [
        'Garden top grow could.',
        'Affect difference world deep against mean.',
      ],
      requirements: [
        'Hand practice also theory.',
        'Side perhaps order answer blue.',
      ],
      benefits: ['Answer break know.', 'Natural listen check receive.'],
      salaryMin: 6805,
      salaryMax: 10884,
      salaryCurrency: 'EUR',
      salaryPeriod: 'HOURLY',
      status: 'CLOSED',
      createdById: 'e0e02123-e0aa-4edb-b1dd-1761345cc5a4',
    },
  });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Elizabeth',
  //     lastName: 'Stanley',
  //     title: 'Scientist, product/process development',
  //     summary: `Paper case store bit responsibility brother box blood. Participant sing story win group.`,
  //     phone: '374-470-1204x556',
  //     linkedin: 'https://linkedin.com/in/daviskatherine',
  //     skills: ['answer', 'act', 'together'],
  //     hourlyRate: 45,
  //     availability: 'Immediate',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Matthew',
  //     lastName: 'Murphy',
  //     title: 'Primary school teacher',
  //     summary: `Turn both in animal energy fire relate. Should office north within prove full mission.`,
  //     phone: '************',
  //     linkedin: 'https://linkedin.com/in/bmcgee',
  //     skills: ['board', 'international', 'itself'],
  //     hourlyRate: 74,
  //     availability: '1 month',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Joshua',
  //     lastName: 'Petersen',
  //     title: 'Trade mark attorney',
  //     summary: `Source catch owner though mention successful natural. Republican very plan cut person fact.`,
  //     phone: '************',
  //     linkedin: 'https://linkedin.com/in/lisa19',
  //     skills: ['trip', 'Republican', 'sister'],
  //     hourlyRate: 87,
  //     availability: 'Immediate',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Courtney',
  //     lastName: 'Holland',
  //     title: 'Neurosurgeon',
  //     summary: `Six sometimes explain professor unit hold what. Whole approach season data than team.`,
  //     phone: '001-************',
  //     linkedin: 'https://linkedin.com/in/tracy98',
  //     skills: ['help', 'baby', 'step'],
  //     hourlyRate: 109,
  //     availability: 'Immediate',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Kelly',
  //     lastName: 'Krueger',
  //     title: 'Scientist, research (life sciences)',
  //     summary: `City myself idea glass third professional former. Despite sell most you statement.`,
  //     phone: '******-616-4840x858',
  //     linkedin: 'https://linkedin.com/in/stucker',
  //     skills: ['water', 'reach', 'data'],
  //     hourlyRate: 82,
  //     availability: '2 weeks',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Bradley',
  //     lastName: 'Davis',
  //     title: 'Soil scientist',
  //     summary: `Approach some newspaper offer. Treat imagine later identify first.`,
  //     phone: '******-508-8210',
  //     linkedin: 'https://linkedin.com/in/armstrongphillip',
  //     skills: ['officer', 'your', 'bed'],
  //     hourlyRate: 92,
  //     availability: 'Immediate',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Paul',
  //     lastName: 'Berger',
  //     title: 'Local government officer',
  //     summary: `Town surface central contain pattern education. Consider son system surface.`,
  //     phone: '(053)364-5257',
  //     linkedin: 'https://linkedin.com/in/xgriffin',
  //     skills: ['focus', 'message', 'ok'],
  //     hourlyRate: 132,
  //     availability: '1 month',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Matthew',
  //     lastName: 'Miller',
  //     title: 'Health visitor',
  //     summary: `Hotel through operation tax card may. However maybe production summer.`,
  //     phone: '449.892.6659x18463',
  //     linkedin: 'https://linkedin.com/in/wgomez',
  //     skills: ['message', 'structure', 'meet'],
  //     hourlyRate: 97,
  //     availability: '1 month',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Adriana',
  //     lastName: 'Macias',
  //     title: 'Printmaker',
  //     summary: `Baby poor deep cost gun. Produce increase form box.`,
  //     phone: '************',
  //     linkedin: 'https://linkedin.com/in/cindydixon',
  //     skills: ['computer', 'shake', 'rich'],
  //     hourlyRate: 22,
  //     availability: '1 month',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Jesse',
  //     lastName: 'Garza',
  //     title: 'Event organiser',
  //     summary: `Mission mind heart day sound impact blood.`,
  //     phone: '***********-8902x601',
  //     linkedin: 'https://linkedin.com/in/thomasburke',
  //     skills: ['behavior', 'attack', 'everyone'],
  //     hourlyRate: 96,
  //     availability: '1 month',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Bobby',
  //     lastName: 'Johnson',
  //     title: 'Music tutor',
  //     summary: `Student green single build bad. Pass share must amount lot per manage.`,
  //     phone: '**********',
  //     linkedin: 'https://linkedin.com/in/joel79',
  //     skills: ['gun', 'history', 'create'],
  //     hourlyRate: 46,
  //     availability: 'Immediate',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Mr.',
  //     lastName: 'Michael Watson PhD',
  //     title: 'Health service manager',
  //     summary: `Number without rather sport. Sound new task cultural son. List act instead care.`,
  //     phone: '001-455-877-1694x72256',
  //     linkedin: 'https://linkedin.com/in/esweeney',
  //     skills: ['base', 'color', 'gun'],
  //     hourlyRate: 87,
  //     availability: 'Immediate',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Evan',
  //     lastName: 'Carter',
  //     title: 'Geologist, engineering',
  //     summary: `Something others someone nature country think.`,
  //     phone: '001-973-502-0802x144',
  //     linkedin: 'https://linkedin.com/in/jenniferacosta',
  //     skills: ['early', 'believe', 'city'],
  //     hourlyRate: 47,
  //     availability: '1 month',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Daniel',
  //     lastName: 'Fisher',
  //     title: 'Research scientist (physical sciences)',
  //     summary: `Program decade home which view city rock. Minute education police cup thought tell design.`,
  //     phone: '(132)719-5005x281',
  //     linkedin: 'https://linkedin.com/in/tonystewart',
  //     skills: ['open', 'story', 'quite'],
  //     hourlyRate: 59,
  //     availability: '2 weeks',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Renee',
  //     lastName: 'Gonzales',
  //     title: 'Engineer, automotive',
  //     summary: `Group care together magazine join far.`,
  //     phone: '******-878-2945x65118',
  //     linkedin: 'https://linkedin.com/in/williamssharon',
  //     skills: ['could', 'TV', 'almost'],
  //     hourlyRate: 92,
  //     availability: '1 month',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'John',
  //     lastName: 'Garcia',
  //     title: 'Teacher, English as a foreign language',
  //     summary: `American audience fight war whether best rise. History cover prevent now way next newspaper.`,
  //     phone: '566-593-9836x84577',
  //     linkedin: 'https://linkedin.com/in/csmith',
  //     skills: ['feel', 'pick', 'admit'],
  //     hourlyRate: 73,
  //     availability: '1 month',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Wendy',
  //     lastName: 'Turner',
  //     title: 'Hydrologist',
  //     summary: `Talk against state ready every. Memory number occur behind leave choose enter.`,
  //     phone: '******-048-0589x2583',
  //     linkedin: 'https://linkedin.com/in/brentdennis',
  //     skills: ['increase', 'like', 'soldier'],
  //     hourlyRate: 107,
  //     availability: 'Immediate',
  //     createdBy: "",
  //   },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Kathy',
  //     lastName: 'Walker',
  //     title: 'Horticultural consultant',
  //     summary: `Car bar number any. Energy huge its recently seven lose. Maintain present cultural pattern save.`,
  //     phone: '(979)270-3352x110',
  //     linkedin: 'https://linkedin.com/in/stacymcintosh',
  //     skills: ['trouble', 'there', 'ten'],
  //     hourlyRate: 87,
  //     availability: '1 month',
  //     createdBy: "",
  //   },
  // });

  //   await prisma.candidate.create({
  //     data: {
  //       firstName: 'Greg',
  //       lastName: 'Brock',
  //       title: 'Industrial/product designer',
  //       summary: `Home even system process.
  // Fall third hit. Meet report tough machine others because.`,
  //       phone: '404.608.4325x62168',
  //       linkedin: 'https://linkedin.com/in/robertberry',
  //       skills: ['last', 'forget', 'where'],
  //       hourlyRate: 145,
  //       availability: '2 weeks',
  //       createdBy: "",
  //     },
  // });

  // await prisma.candidate.create({
  //   data: {
  //     firstName: 'Joann',
  //     lastName: 'Anderson',
  //     title: 'Corporate investment banker',
  //     summary: `Blood continue several partner environment. Partner others discuss ago bag sister care.`,
  //     phone: '001-569-998-8081x7703',
  //     linkedin: 'https://linkedin.com/in/cterrell',
  //     skills: ['open', 'impact', 'name'],
  //     hourlyRate: 33,
  //     availability: 'Immediate',
  //     createdBy: "",
  //   },
  // });
}

main()
  .then(() => prisma.$disconnect())
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
