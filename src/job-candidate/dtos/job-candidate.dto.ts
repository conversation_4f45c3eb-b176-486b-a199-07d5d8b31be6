import { CandidateQualification, CandidateStatus } from '@prisma/client';
import { IsNotEmpty, IsOptional, IsEnum, IsInt, IsString } from 'class-validator';

export class UpdateJobCandidateDto {
  @IsNotEmpty()
  @IsInt()
  jobId: number;

  @IsNotEmpty()
  @IsInt()
  candidateId: number;

  @IsOptional()
  @IsEnum(CandidateStatus)
  status: CandidateStatus;

  @IsOptional()
  @IsEnum(CandidateQualification)
  qualified: CandidateQualification;

  @IsOptional()
  @IsString()
  note: string;
}
