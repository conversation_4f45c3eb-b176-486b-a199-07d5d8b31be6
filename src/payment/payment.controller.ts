import { Controller, Post, Body } from '@nestjs/common';
import { PaymentService } from './payment.service';
import {
  AttachPaymentMethodDto,
  CreateCustomerDto,
  CreateSubscriptionDto,
} from 'src/payment/dtos/payment.dto';

// TODO, require x-api-key or integrate @UseGuards(AuthGuard)
@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Post('create-setup-intent')
  async createSetupIntent() {
    return this.paymentService.createSetupIntent();
  }

  @Post('create-customer')
  async createCustomer(@Body() dto: CreateCustomerDto) {
    return this.paymentService.createCustomer(dto);
  }

  @Post('attach-payment-method')
  async attachPaymentMethod(@Body() dto: AttachPaymentMethodDto) {
    return this.paymentService.attachPaymentMethodToCustomer(dto);
  }

  @Post('create-subscription')
  async createSubscription(@Body() dto: CreateSubscriptionDto) {
    return this.paymentService.createSubscription(dto);
  }
}
