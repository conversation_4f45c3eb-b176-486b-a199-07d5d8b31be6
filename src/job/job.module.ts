import { <PERSON>du<PERSON> } from '@nestjs/common';
import { Job<PERSON><PERSON>roller } from './job.controller';
import { JobService } from './job.service';
import { PrismaService } from 'src/prisma.service';
import { NotificationModule } from 'src/notification/notification.module';
import { UserActivityModule } from 'src/user-activity/user-activity.module';

@Module({
  imports: [NotificationModule, UserActivityModule],
  controllers: [JobController],
  providers: [JobService, PrismaService],
})
export class JobModule {}
