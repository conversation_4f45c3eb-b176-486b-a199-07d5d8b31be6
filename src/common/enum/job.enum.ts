export enum JobType {
  FULL_TIME,
  PART_TIME,
  CONTRA<PERSON>,
  TEMPORARY,
  INTERNSHIP,
  REMOTE,
}

export const JobTypeLabels: Record<JobType, string> = {
  [JobType.FULL_TIME]: 'Full-time',
  [JobType.PART_TIME]: 'Part-time',
  [JobType.CONTRACT]: 'Contract',
  [JobType.TEMPORARY]: 'Temporary',
  [JobType.INTERNSHIP]: 'Internship',
  [JobType.REMOTE]: 'Remote',
};

export enum JobStatus {
  DRAFT,
  ACTIVE,
  PAUSED,
  CLOSED,
}

export const JobStatusLabels: Record<JobStatus, string> = {
  [JobStatus.DRAFT]: 'Draft',
  [JobStatus.ACTIVE]: 'Active',
  [JobStatus.PAUSED]: 'Paused',
  [JobStatus.CLOSED]: 'Closed',
};
