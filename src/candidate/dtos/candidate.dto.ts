import { Candidate, CandidateQualification } from '@prisma/client';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsArray, IsString } from 'class-validator';

export class CreateCandidateDto {
  @IsOptional()
  firstName: string;

  @IsOptional()
  lastName: string;

  @IsNotEmpty()
  linkedin: string;

  @IsOptional()
  email: string;

  @IsOptional()
  company: string;

  @IsOptional()
  title: string;

  @IsOptional()
  location: string;

  @IsOptional()
  @IsNumber()
  jobId: number;

  @IsOptional()
  summary: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  skills: string[];

  @IsOptional()
  phone: string;

  @IsOptional()
  @IsNumber()
  hourlyRate: number;

  @IsOptional()
  availability: string;

  @IsOptional()
  note: string;
}

export interface CandidateFilterType {
  items_per_page?: number;
  page?: number;
  search?: string;
}

export interface CandidatePaginationResponseType {
  data: Candidate[];
  total: number;
  currentPage: number;
  itemsPerPage: number;
}

export class UpdateCandidateDto {
  @IsOptional()
  firstName: string;

  @IsOptional()
  lastName: string;

  @IsNotEmpty()
  title: string;

  @IsNotEmpty()
  summary: string;

  @IsOptional()
  @IsEnum(CandidateQualification)
  qualified: CandidateQualification;
}

export class AddCandidateNoteDto {
  content: string;
}

export class CreateCandidateWithJobDto extends CreateCandidateDto {
  @IsOptional()
  @IsEnum(CandidateQualification)
  qualified?: CandidateQualification;

  @IsOptional()
  jobNote?: string;
}
