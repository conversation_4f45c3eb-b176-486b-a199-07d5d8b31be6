// import { PrismaClient, UserRole } from '@prisma/client';
// const prisma = new PrismaClient();
// async function main() {

//   const admin = await prisma.user.create({
//     data: {
//       email: '<EMAIL>',
//       password: 'hashed-admin-password',
//       firstName: 'Admin',
//       lastName: 'User',
//       role: 'ADMIN',
//       country: 'US',
//     },
//   });

//   const agency1 = await prisma.agency.create({
//     data: {
//       name: "Agency1 Inc.",
//       description: "Description for Agency1 Inc."
//     },
//   });

//   const agencyUser1 = await prisma.user.create({
//     data: {
//       email: '<EMAIL>',
//       password: 'hashed-password-agency1',
//       firstName: 'Agency1',
//       lastName: 'Owner',
//       role: 'AGENCY',
//       country: 'US',
//       agencyId: agency1.id,
//     },
//   });

//   const teamUser1 = await prisma.user.create({
//     data: {
//       email: '<EMAIL>',
//       password: 'hashed-password-team1',
//       firstName: 'Team1',
//       lastName: 'Member',
//       role: 'TEAM',
//       country: 'US',
//       agencyId: agency1.id,
//     },
//   });

//   await prisma.client.create({
//     data: {
//       companyName: `Fleming LLC`,
//       email: `<EMAIL>`,
//       industry: `Methodologies`,
//       status: 'ACTIVE',
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.client.create({
//     data: {
//       companyName: `Stanley Group`,
//       email: `<EMAIL>`,
//       industry: `E-commerce`,
//       status: 'ACTIVE',
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.client.create({
//     data: {
//       companyName: `Haynes-Brewer`,
//       email: `<EMAIL>`,
//       industry: `Roi`,
//       status: 'ACTIVE',
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Cynthia Gomez`,
//       title: `Teacher, secondary school`,
//       summary: `Fire visit four specific day feeling what sit. Share than cost science.`,
//       skills: ['line', 'send', 'one'],
//       linkedin: `https://www.scott.net/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Lauren Rojas`,
//       title: `Commercial horticulturist`,
//       summary: `Similar perhaps easy force. Recognize must probably or. College exactly follow within buy.`,
//       skills: ['feeling', 'gun', 'artist'],
//       linkedin: `http://roach.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Nicholas Moore`,
//       title: `Designer, industrial/product`,
//       summary: `Between price tree argue. Everybody month take billion relate life.`,
//       skills: ['item', 'research', 'sense'],
//       linkedin: `http://www.allen.biz/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Bobby Miller`,
//       title: `Licensed conveyancer`,
//       summary: `Old she turn tonight. Throughout college tough around. Region during white everything long.`,
//       skills: ['impact', 'travel', 'space'],
//       linkedin: `http://kennedy.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Lisa Bennett`,
//       title: `Programmer, multimedia`,
//       summary: `Brother service north machine prepare hundred. Wear commercial source more method.`,
//       skills: ['sing', 'worry', 'they'],
//       linkedin: `http://www.patel.org/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Daniel Richards`,
//       title: `Regulatory affairs officer`,
//       summary: `Material across stage look table bring. Agent knowledge between become president find traditional.`,
//       skills: ['military', 'well', 'form'],
//       linkedin: `http://www.brown.org/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Tracy Stevens`,
//       title: `Nurse, learning disability`,
//       summary: `Why drop maintain effort race way. Well result man fine your.`,
//       skills: ['four', 'above', 'computer'],
//       linkedin: `http://www.jones.info/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Travis Wright`,
//       title: `Ceramics designer`,
//       summary: `Food mission put sea performance number live practice. Clear line financial say yard play while.`,
//       skills: ['special', 'her', 'magazine'],
//       linkedin: `http://www.mendoza.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Brittany Bullock`,
//       title: `Surveyor, hydrographic`,
//       summary: `Fear radio always garden drive approach check.
// Require represent try group.`,
//       skills: ['father', 'too', 'guy'],
//       linkedin: `https://www.mann.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Charles Hamilton`,
//       title: `Scientist, research (physical sciences)`,
//       summary: `Science stop do might. Lose police media reason.`,
//       skills: ['interest', 'animal', 'factor'],
//       linkedin: `http://www.baker-russell.org/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Jennifer Garcia MD`,
//       title: `Accounting technician`,
//       summary: `Visit despite third huge entire natural. Second collection thousand indicate camera.`,
//       skills: ['whole', 'attack', 'general'],
//       linkedin: `https://www.lowe-mitchell.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Benjamin Brown`,
//       title: `Surveyor, land/geomatics`,
//       summary: `Include material instead along possible people.`,
//       skills: ['interesting', 'than', 'Republican'],
//       linkedin: `https://norris.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Yvette Rodriguez`,
//       title: `Lecturer, higher education`,
//       summary: `Effect pass after. Since place your year win blood lot. Anything why sell foreign yeah.`,
//       skills: ['write', 'field', 'set'],
//       linkedin: `https://gonzales.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Cody Wilkinson`,
//       title: `Chemical engineer`,
//       summary: `Into performance southern. Notice if item on him week step. Vote word movement service.`,
//       skills: ['never', 'recognize', 'morning'],
//       linkedin: `https://www.lewis.org/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Joyce Sandoval`,
//       title: `Counselling psychologist`,
//       summary: `These people a son condition support plan know. Pick consumer budget cell want own.`,
//       skills: ['commercial', 'expect', 'defense'],
//       linkedin: `https://www.norris.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `David Myers`,
//       title: `Medical technical officer`,
//       summary: `Southern peace fund woman whose senior time. Indicate impact quality family.`,
//       skills: ['standard', 'over', 'third'],
//       linkedin: `http://owens.org/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Jimmy Contreras`,
//       title: `Community pharmacist`,
//       summary: `Reveal well star now choose change event contain. Interest use TV play respond house.`,
//       skills: ['score', 'long', 'what'],
//       linkedin: `https://rosales.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Jill Perez`,
//       title: `Engineer, aeronautical`,
//       summary: `Every nice environmental. Along season program letter environmental project most image.`,
//       skills: ['discover', 'purpose', 'exist'],
//       linkedin: `http://dean.org/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Catherine Graham`,
//       title: `Artist`,
//       summary: `Product significant nor behavior. Board alone stuff.`,
//       skills: ['leg', 'great', 'short'],
//       linkedin: `http://www.tucker-roberts.net/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Tami Navarro`,
//       title: `Educational psychologist`,
//       summary: `There rate business. Teacher school act leader instead break.`,
//       skills: ['that', 'consumer', 'measure'],
//       linkedin: `http://campos-kelley.com/`,
//       agencyId: agency1.id,
//       createdById: teamUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Pharmacist, community`,
//       description: `Some picture something author away effect. The themselves age issue hope.`,
//       keySkills: ['attorney', 'day', 'with'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 1 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Technical sales engineer`,
//       description: `Analysis hour tell probably. Consumer letter system body.`,
//       keySkills: ['continue', 'fire', 'practice'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 2 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Marketing executive`,
//       description: `Himself find season truth safe born member.`,
//       keySkills: ['perhaps', 'language', 'never'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 3 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Journalist, newspaper`,
//       description: `Professional respond Congress establish serve. Magazine radio learn consumer democratic.`,
//       keySkills: ['responsibility', 'building', 'second'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 4 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Medical physicist`,
//       description: `Station these official sing couple myself rather. Student open movement after treat.`,
//       keySkills: ['executive', 'by', 'quality'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 5 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Media buyer`,
//       description: `Choice size star life blue value lead claim. Call pull project human perhaps.`,
//       keySkills: ['cultural', 'second', 'environment'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 6 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Physicist, medical`,
//       description: `Stop gun pattern local. Produce fund yeah identify TV.`,
//       keySkills: ['we', 'money', 'food'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 7 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Structural engineer`,
//       description: `Stage our can. Check test should argue support wish. Officer seem street cost of chance also agent.`,
//       keySkills: ['city', 'baby', 'participant'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 8 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Engineer, mining`,
//       description: `Have population still quality section while. Listen attorney key standard.`,
//       keySkills: ['which', 'career', 'consumer'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 9 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Lighting technician, broadcasting/film/video`,
//       description: `Message head major without maintain nearly.`,
//       keySkills: ['generation', 'carry', 'listen'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 10 } },
//       agencyId: agency1.id,
//       createdById: agencyUser1.id,
//     },
//   });

//   const agency2 = await prisma.agency.create({
//     data: {
//       name: "Agency2 Inc.",
//       description: "Description for Agency2 Inc."
//     },
//   });

//   const agencyUser2 = await prisma.user.create({
//     data: {
//       email: '<EMAIL>',
//       password: 'hashed-password-agency2',
//       firstName: 'Agency2',
//       lastName: 'Owner',
//       role: 'AGENCY',
//       country: 'US',
//       agencyId: agency2.id,
//     },
//   });

//   const teamUser2 = await prisma.user.create({
//     data: {
//       email: '<EMAIL>',
//       password: 'hashed-password-team2',
//       firstName: 'Team2',
//       lastName: 'Member',
//       role: 'TEAM',
//       country: 'US',
//       agencyId: agency2.id,
//     },
//   });

//   await prisma.client.create({
//     data: {
//       companyName: `Kelley-Pacheco`,
//       email: `<EMAIL>`,
//       industry: `Info-mediaries`,
//       status: 'ACTIVE',
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.client.create({
//     data: {
//       companyName: `Adams Inc`,
//       email: `<EMAIL>`,
//       industry: `Interfaces`,
//       status: 'ACTIVE',
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.client.create({
//     data: {
//       companyName: `Fischer-Hines`,
//       email: `<EMAIL>`,
//       industry: `Interfaces`,
//       status: 'ACTIVE',
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Jessica Salas`,
//       title: `Building surveyor`,
//       summary: `Answer especially fill send side. Natural necessary without son east political ok.`,
//       skills: ['rate', 'plan', 'level'],
//       linkedin: `https://www.butler.net/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Abigail Bass`,
//       title: `Barrister's clerk`,
//       summary: `List treat avoid father. Physical successful pay amount same.
// Mouth large worry wonder wish.`,
//       skills: ['hair', 'wide', 'also'],
//       linkedin: `https://rogers.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `David Smith`,
//       title: `Forensic scientist`,
//       summary: `Continue whom thus moment. Decision budget edge carry about career.
// Dinner blue picture media us.`,
//       skills: ['vote', 'parent', 'material'],
//       linkedin: `https://www.ruiz-williams.org/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Frank Smith`,
//       title: `Artist`,
//       summary: `Early floor five it. Listen upon anything night especially large.`,
//       skills: ['deep', 'represent', 'fish'],
//       linkedin: `https://www.hernandez.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Scott Brown`,
//       title: `Dietitian`,
//       summary: `Star international new peace. Bring person might table appear participant certainly.`,
//       skills: ['reflect', 'season', 'job'],
//       linkedin: `http://rodriguez-travis.org/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Luke Howard`,
//       title: `Health physicist`,
//       summary: `Man enough walk trial. Science yourself hope design. Billion between effort nearly tough firm.`,
//       skills: ['management', 'consider', 'weight'],
//       linkedin: `http://www.stewart-rodriguez.info/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Courtney Brandt`,
//       title: `Ceramics designer`,
//       summary: `Oil ground life allow fall report. Evening than wrong identify.`,
//       skills: ['resource', 'among', 'nature'],
//       linkedin: `http://www.russell-chase.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Robert Eaton`,
//       title: `Clothing/textile technologist`,
//       summary: `Share number tend manage. Difference foot anything family section skin control month.`,
//       skills: ['west', 'page', 'card'],
//       linkedin: `http://www.dunn.info/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Betty Hartman`,
//       title: `Runner, broadcasting/film/video`,
//       summary: `Right student his subject learn. Professional start knowledge agreement attorney.`,
//       skills: ['well', 'amount', 'relationship'],
//       linkedin: `https://www.hernandez.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Heather Fisher`,
//       title: `Company secretary`,
//       summary: `Every size try pass. Total wide movement authority end.`,
//       skills: ['sea', 'box', 'soon'],
//       linkedin: `http://james-davis.org/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Alicia Hernandez`,
//       title: `Media buyer`,
//       summary: `Science decision occur people. Military improve artist energy start.`,
//       skills: ['break', 'employee', 'another'],
//       linkedin: `https://www.moore.org/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Virginia Flores`,
//       title: `Geoscientist`,
//       summary: `Want community remain run brother stock weight.`,
//       skills: ['view', 'you', 'drop'],
//       linkedin: `http://thomas-payne.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Mr. Corey Oliver`,
//       title: `Teacher, secondary school`,
//       summary: `Pass speak listen green like. Wall alone memory media.`,
//       skills: ['station', 'bank', 'little'],
//       linkedin: `https://miller.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Ronnie Tate`,
//       title: `Futures trader`,
//       summary: `Themselves citizen certainly matter probably. Coach continue product natural happen them sport.`,
//       skills: ['forward', 'often', 'country'],
//       linkedin: `http://www.ward.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Michael Cox`,
//       title: `Agricultural consultant`,
//       summary: `Between decade season soon difference mean. Sense follow marriage state.`,
//       skills: ['billion', 'open', 'general'],
//       linkedin: `https://frederick-bailey.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Emily Smith`,
//       title: `Psychotherapist, dance movement`,
//       summary: `Best heavy represent future her cell fast. During happen citizen mind growth policy.`,
//       skills: ['woman', 'respond', 'middle'],
//       linkedin: `http://www.gomez-pruitt.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Justin Williams`,
//       title: `Museum/gallery conservator`,
//       summary: `Dream expect whether sing. Beautiful force perform move both one. Explain thing relate at.`,
//       skills: ['democratic', 'personal', 'pattern'],
//       linkedin: `https://www.vasquez-mcpherson.com/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Elaine Gross`,
//       title: `Engineer, land`,
//       summary: `Professional throughout grow himself. Data participant back upon.`,
//       skills: ['recognize', 'entire', 'defense'],
//       linkedin: `https://moore.org/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Jason Reyes`,
//       title: `Marine scientist`,
//       summary: `Market involve past focus. Young they admit under executive. Both wide my plant.`,
//       skills: ['her', 'source', 'field'],
//       linkedin: `https://www.simpson-young.info/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Travis Brown`,
//       title: `Amenity horticulturist`,
//       summary: `Month part American maybe might. Them community action. Do room top all take deal evidence go.`,
//       skills: ['teach', 'deep', 'much'],
//       linkedin: `https://www.nash.org/`,
//       agencyId: agency2.id,
//       createdById: teamUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Conference centre manager`,
//       description: `Well out inside many. Item hear stage involve effect. Might maintain fear focus.`,
//       keySkills: ['listen', 'door', 'thus'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 11 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Engineer, building services`,
//       description: `Commercial white try trade. Arrive front floor benefit particularly.`,
//       keySkills: ['education', 'story', 'water'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 12 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Hydrologist`,
//       description: `Lay PM choose. Task show newspaper material sometimes many.`,
//       keySkills: ['food', 'husband', 'occur'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 13 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Manufacturing engineer`,
//       description: `Fund blood series compare accept firm. Sit likely responsibility back manage culture make.`,
//       keySkills: ['entire', 'hear', 'get'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 14 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Corporate treasurer`,
//       description: `Personal page practice knowledge detail able. Choose enough back choose common growth.`,
//       keySkills: ['attention', 'knowledge', 'center'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 15 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Tourist information centre manager`,
//       description: `Security customer find space current support skill. Until half political black others.`,
//       keySkills: ['every', 'certainly', 'recognize'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 16 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Consulting civil engineer`,
//       description: `Minute need security play between citizen. Somebody decision style example.`,
//       keySkills: ['whole', 'side', 'natural'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 17 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Music therapist`,
//       description: `Discover travel team how. However democratic light parent risk together voice almost.`,
//       keySkills: ['finally', 'partner', 'become'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 18 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Associate Professor`,
//       description: `Stock station size try physical. Movement indicate music central environment site.`,
//       keySkills: ['nothing', 'ago', 'produce'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 19 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Data scientist`,
//       description: `Century operation wait event fly whose you. Floor senior baby yard each.`,
//       keySkills: ['hope', 'check', 'out'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 20 } },
//       agencyId: agency2.id,
//       createdById: agencyUser2.id,
//     },
//   });

//   const agency3 = await prisma.agency.create({
//     data: {
//       name: "Agency3 Inc.",
//       description: "Description for Agency3 Inc."
//     },
//   });

//   const agencyUser3 = await prisma.user.create({
//     data: {
//       email: '<EMAIL>',
//       password: 'hashed-password-agency3',
//       firstName: 'Agency3',
//       lastName: 'Owner',
//       role: 'AGENCY',
//       country: 'US',
//       agencyId: agency3.id,
//     },
//   });

//   const teamUser3 = await prisma.user.create({
//     data: {
//       email: '<EMAIL>',
//       password: 'hashed-password-team3',
//       firstName: 'Team3',
//       lastName: 'Member',
//       role: 'TEAM',
//       country: 'US',
//       agencyId: agency3.id,
//     },
//   });

//   await prisma.client.create({
//     data: {
//       companyName: `West, Diaz and Singleton`,
//       email: `<EMAIL>`,
//       industry: `Relationships`,
//       status: 'ACTIVE',
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.client.create({
//     data: {
//       companyName: `Stanley, Torres and Alvarado`,
//       email: `<EMAIL>`,
//       industry: `Models`,
//       status: 'ACTIVE',
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.client.create({
//     data: {
//       companyName: `Jackson-Finley`,
//       email: `<EMAIL>`,
//       industry: `Initiatives`,
//       status: 'ACTIVE',
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Dawn Caldwell`,
//       title: `Heritage manager`,
//       summary: `Face school give thought. Production wrong direction. Style attention learn important.`,
//       skills: ['land', 'behavior', 'office'],
//       linkedin: `https://wall.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Elizabeth Simon`,
//       title: `Purchasing manager`,
//       summary: `Bed hard agency quality attorney through. Stay knowledge model care action total.`,
//       skills: ['lay', 'add', 'step'],
//       linkedin: `http://jones-roberts.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Maria Graham`,
//       title: `Psychiatrist`,
//       summary: `Cut visit onto book rise wear. Quite second truth south really.`,
//       skills: ['significant', 'phone', 'have'],
//       linkedin: `https://www.perry.net/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Joshua Pruitt`,
//       title: `Sales promotion account executive`,
//       summary: `Consumer political oil million they. Like amount walk rate car sea.`,
//       skills: ['exactly', 'fear', 'heart'],
//       linkedin: `http://jackson-robinson.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Taylor Joyce PhD`,
//       title: `Facilities manager`,
//       summary: `Item ability sell resource high be seek compare. Model road remain several sit.`,
//       skills: ['thought', 'gas', 'respond'],
//       linkedin: `https://jones.biz/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Alan Haynes`,
//       title: `Government social research officer`,
//       summary: `Stock little wish. Read only own. Hit end report example.`,
//       skills: ['buy', 'radio', 'understand'],
//       linkedin: `https://johnson.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Tina Kelly`,
//       title: `Engineer, electrical`,
//       summary: `Education pattern building include detail. Guess music never mind. Woman if executive way.`,
//       skills: ['hour', 'fight', 'run'],
//       linkedin: `http://www.rogers.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Kristin Hart`,
//       title: `Video editor`,
//       summary: `Writer clear view together order here. Simple either young not poor purpose relationship.`,
//       skills: ['suffer', 'television', 'color'],
//       linkedin: `https://howard-jones.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `John Russell`,
//       title: `Programmer, systems`,
//       summary: `Every meet five role development. Bit ball notice think week receive.`,
//       skills: ['smile', 'news', 'level'],
//       linkedin: `https://www.reed-townsend.biz/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Andrea Walton`,
//       title: `Musician`,
//       summary: `Establish information wear service operation reach music.`,
//       skills: ['foreign', 'real', 'before'],
//       linkedin: `http://valentine-davenport.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Kevin Russell`,
//       title: `Civil Service administrator`,
//       summary: `Cell develop tend actually cultural thing article.`,
//       skills: ['type', 'recently', 'gun'],
//       linkedin: `https://www.henderson-keith.biz/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Katie Lopez`,
//       title: `Adult guidance worker`,
//       summary: `To debate laugh. Cold spend effect reveal address point relationship resource.`,
//       skills: ['hundred', 'water', 'camera'],
//       linkedin: `http://www.barron-berry.info/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Daniel Smith`,
//       title: `Copy`,
//       summary: `Myself mouth though human store else. Specific talk so day.`,
//       skills: ['how', 'daughter', 'class'],
//       linkedin: `http://www.walker-jordan.info/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Whitney Garcia`,
//       title: `Engineer, energy`,
//       summary: `Green bill tree partner day sure. White reduce lawyer all sell.`,
//       skills: ['lay', 'just', 'run'],
//       linkedin: `http://www.conway-gutierrez.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Aaron Walter`,
//       title: `Accountant, chartered public finance`,
//       summary: `Conference billion simple onto claim. Radio summer parent song.`,
//       skills: ['organization', 'drive', 'including'],
//       linkedin: `http://owen.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Jody Anderson`,
//       title: `Lawyer`,
//       summary: `Check street rule with price structure. Begin doctor without. Side become body light push one.`,
//       skills: ['nearly', 'today', 'each'],
//       linkedin: `https://www.edwards.net/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Samantha Johnson`,
//       title: `Contractor`,
//       summary: `Decide current receive memory particularly manager responsibility.
// Share above side north.`,
//       skills: ['to', 'move', 'ask'],
//       linkedin: `http://webb.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Angela Abbott`,
//       title: `Armed forces technical officer`,
//       summary: `Least every lose success leave total. Offer top authority put inside year water.`,
//       skills: ['produce', 'enter', 'help'],
//       linkedin: `https://daniels-smith.info/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Laura Andrews`,
//       title: `Prison officer`,
//       summary: `Positive thank able. Lead available source professional I knowledge likely.`,
//       skills: ['tonight', 'back', 'end'],
//       linkedin: `http://cole-anderson.com/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.candidate.create({
//     data: {
//       name: `Jessica Anderson`,
//       title: `Trade mark attorney`,
//       summary: `Might spring could but miss bad thing. Prevent relate range cost later little.`,
//       skills: ['oil', 'instead', 'under'],
//       linkedin: `http://www.houston.org/`,
//       agencyId: agency3.id,
//       createdById: teamUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Operations geologist`,
//       description: `Part themselves health generation speech. Defense million town large. Knowledge young career.`,
//       keySkills: ['sell', 'deep', 'or'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 21 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Industrial/product designer`,
//       description: `For capital firm sit. Miss team management throw bad change near. Score the form strong sea.`,
//       keySkills: ['might', 'audience', 'half'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 22 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Data processing manager`,
//       description: `Create number future consider describe commercial necessary. Suffer play act interesting yet.`,
//       keySkills: ['discover', 'ever', 'somebody'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 23 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Psychologist, occupational`,
//       description: `Who level source week better. Reach clear admit thousand season might I.`,
//       keySkills: ['special', 'serious', 'particularly'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 24 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Engineer, energy`,
//       description: `Everybody about he like again. Too such significant tell. Analysis eat to community discuss.`,
//       keySkills: ['third', 'hold', 'evidence'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 25 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Pathologist`,
//       description: `Cup ever successful dinner. Picture activity start today. Inside tonight senior about.`,
//       keySkills: ['build', 'wrong', 'where'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 26 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Pharmacist, hospital`,
//       description: `Cultural toward usually feeling peace. Car movie expert whom system evening.`,
//       keySkills: ['main', 'long', 'maintain'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 27 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Scientific laboratory technician`,
//       description: `By plant myself board sport support point. Order happy work this effort ground investment.`,
//       keySkills: ['price', 'mean', 'effort'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 28 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Retail banker`,
//       description: `Where building include finish experience ever. Join late explain area late again court.`,
//       keySkills: ['apply', 'take', 'market'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 29 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });

//   await prisma.job.create({
//     data: {
//       title: `Retail buyer`,
//       description: `Claim half color garden. Drive yeah modern real. Memory if arrive model fill position.`,
//       keySkills: ['consider', 'sing', 'thousand'],
//       responsibilities: ["Task A", "Task B"],
//       requirements: ["Req A", "Req B"],
//       benefits: ["Benefit A", "Benefit B"],
//       type: 'FULL_TIME',
//       workLocation: 'REMOTE',
//       salaryCurrency: 'USD',
//       salaryPeriod: 'MONTHLY',
//       client: { connect: { id: 30 } },
//       agencyId: agency3.id,
//       createdById: agencyUser3.id,
//     },
//   });
// }

// main()
//   .then(() => {
//     console.log('🌱 Seed data created successfully');
//   })
//   .catch((e) => {
//     console.error(e);
//     process.exit(1);
//   })
//   .finally(() => {
//     prisma.$disconnect();
//   });
