import { Module } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { ClientController } from './client.controller';
import { ClientService } from './client.service';
import { UserActivityModule } from 'src/user-activity/user-activity.module';

@Module({
  imports: [UserActivityModule],
  controllers: [ClientController],
  providers: [ClientService, PrismaService],
})
export class ClientModule {}
