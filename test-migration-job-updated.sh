#!/bin/bash

# Updated Migration Job API Test Script using cURL
# Tests the POST /jobs/migration endpoint with new payload structure

BASE_URL="http://localhost:3000"
ENDPOINT="/jobs/migration"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Testing Updated Migration Job API${NC}"
echo "Endpoint: ${BASE_URL}${ENDPOINT}"
echo "New payload structure: { client: {}, job: {}, candidates: [] }"
echo ""

# Test 1: Complete migration with new client and candidates
echo -e "${YELLOW}Test 1: Complete Migration (New Client + Job + Candidates)${NC}"
echo "----------------------------------------"

TIMESTAMP=$(date +%s)

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"client\": {
      \"name\": \"Tech Innovations Corp\",
      \"email\": \"contact@techinnovations-${TIMESTAMP}.com\",
      \"industry\": \"Technology\",
      \"location\": \"San Francisco, CA\",
      \"phone\": \"******-0123\",
      \"summary\": \"Leading technology company specializing in web development and AI solutions\"
    },
    \"job\": {
      \"title\": \"Senior Full Stack Developer - Migration Test\",
      \"description\": \"We are looking for an experienced full stack developer to join our growing team. This position was created via migration API with new client.\",
      \"status\": \"DRAFT\",
      \"type\": \"FULL_TIME\",
      \"workLocation\": \"HYBRID\",
      \"department\": \"Engineering\",
      \"location\": \"San Francisco, CA\",
      \"keySkills\": [\"JavaScript\", \"TypeScript\", \"React\", \"Node.js\", \"NestJS\", \"PostgreSQL\"],
      \"responsibilities\": [\"Develop and maintain web applications\", \"Write clean, maintainable code\", \"Collaborate with cross-functional teams\"],
      \"requirements\": [\"5+ years of full stack development experience\", \"Strong knowledge of JavaScript/TypeScript\", \"Experience with React and Node.js\"],
      \"benefits\": [\"Competitive salary\", \"Health insurance\", \"Remote work flexibility\", \"Professional development budget\"],
      \"salaryMin\": 80000,
      \"salaryMax\": 120000,
      \"salaryCurrency\": \"USD\",
      \"salaryPeriod\": \"YEARLY\"
    },
    \"candidates\": [
      {
        \"firstName\": \"Alice\",
        \"lastName\": \"Johnson\",
        \"linkedin\": \"https://linkedin.com/in/alice-johnson-test-${TIMESTAMP}\",
        \"email\": \"<EMAIL>\",
        \"title\": \"Senior Full Stack Developer\",
        \"company\": \"Previous Tech Inc\",
        \"location\": \"San Francisco, CA\",
        \"summary\": \"Experienced full stack developer with 6 years in web development. Proficient in React, Node.js, and cloud technologies.\",
        \"phone\": \"******-0456\",
        \"skills\": [\"React\", \"Node.js\", \"AWS\", \"PostgreSQL\"],
        \"hourlyRate\": 75,
        \"availability\": \"2 weeks notice\",
        \"note\": \"Excellent candidate with strong technical background and leadership experience.\"
      },
      {
        \"firstName\": \"Bob\",
        \"lastName\": \"Smith\",
        \"linkedin\": \"https://linkedin.com/in/bob-smith-dev-${TIMESTAMP}\",
        \"email\": \"<EMAIL>\",
        \"title\": \"Full Stack Engineer\",
        \"company\": \"StartupXYZ\",
        \"location\": \"Austin, TX\",
        \"summary\": \"Full stack engineer with 4 years of experience building scalable web applications.\",
        \"note\": \"Great cultural fit with startup experience.\"
      }
    ]
  }" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo -e "\n${GREEN}✅ Test 1 completed${NC}\n"

# Test 2: Existing client (should find by email)
echo -e "${YELLOW}Test 2: Existing Client Test${NC}"
echo "----------------------------------------"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"client\": {
      \"name\": \"Tech Innovations Corp\",
      \"email\": \"contact@techinnovations-${TIMESTAMP}.com\"
    },
    \"job\": {
      \"title\": \"Backend Developer - Existing Client Test\",
      \"description\": \"Backend developer position for existing client.\",
      \"status\": \"DRAFT\",
      \"type\": \"FULL_TIME\",
      \"workLocation\": \"REMOTE\"
    }
  }" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo -e "\n${GREEN}✅ Test 2 completed${NC}\n"

# Test 3: Job only (no candidates)
echo -e "${YELLOW}Test 3: Job Only (No Candidates)${NC}"
echo "----------------------------------------"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"client\": {
      \"name\": \"New Client Corp\",
      \"email\": \"newclient-${TIMESTAMP}@example.com\",
      \"industry\": \"Finance\"
    },
    \"job\": {
      \"title\": \"Data Analyst - Job Only Test\",
      \"description\": \"Data analyst position created without candidates.\",
      \"status\": \"DRAFT\",
      \"type\": \"FULL_TIME\",
      \"workLocation\": \"FULLY_ONSITE\",
      \"salaryMin\": 60000,
      \"salaryMax\": 85000,
      \"salaryCurrency\": \"USD\",
      \"salaryPeriod\": \"YEARLY\"
    }
  }" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo -e "\n${GREEN}✅ Test 3 completed${NC}\n"

# Test 4: Invalid data (missing required fields)
echo -e "${YELLOW}Test 4: Invalid Data (Should Fail)${NC}"
echo "----------------------------------------"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"client\": {
      \"name\": \"Invalid Client\"
    },
    \"job\": {
      \"description\": \"Job without title - should fail\"
    }
  }" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo -e "\n${RED}❌ Test 4 completed (Expected to fail)${NC}\n"

echo -e "${BLUE}🎉 All tests completed!${NC}"
echo ""
echo -e "${YELLOW}Notes:${NC}"
echo "- Make sure your server is running on ${BASE_URL}"
echo "- The API will automatically create clients if they don't exist (by email)"
echo "- Existing clients will be found by email and reused"
echo "- Authentication headers may be required depending on your setup"
echo "- Check the server logs for detailed information"
