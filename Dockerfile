# Sử dụng Node LTS (Alpine nhỏ gọn)
FROM node:22.18.0-alpine

# C<PERSON><PERSON> các gói cần thiế<PERSON> để build bcrypt và Prisma
RUN apk add --no-cache bash python3 make g++ libc6-compat

# Đặt thư mục làm việc
WORKDIR /app

# Copy package trước (tối ưu cache)
COPY package*.json ./

# Cài dependencies (không chạy scripts khi install)
RUN npm install --ignore-scripts

# Rebuild bcrypt cho kiến trúc của container
RUN npm rebuild bcrypt --build-from-source

# Copy toàn bộ source code
COPY . .

# Generate Prisma Client
RUN npx prisma generate

# Mở cổng 3000
EXPOSE 3000

# Start ở chế độ dev (có thể override bằng docker-compose)
CMD ["npm", "run", "start:dev"]
