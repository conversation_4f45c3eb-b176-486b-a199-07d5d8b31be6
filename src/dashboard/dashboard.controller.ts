import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { DashboardService } from 'src/dashboard/dashboard.service';
import {
  DashboardFilterType,
  DashboardSummary,
} from 'src/dashboard/dtos/dashboard.dto';

@UseGuards(JwtAuthGuard)
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('overview')
  getDashboardOverview(
    @Query() params: DashboardFilterType,
  ): Promise<BaseResponse<DashboardSummary>> {
    return this.dashboardService.getOverview(params);
  }
}
