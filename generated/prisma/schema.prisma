// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int       @id @default(autoincrement())
  email     String    @unique
  password  String
  firstName String
  lastName  String
  jobTitle  String?
  Job       Job[]
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

model Job {
  id          Int       @id @default(autoincrement())
  title       String
  description String    @db.Text
  createdBy   User      @relation(fields: [createdById], references: [id])
  candidate   Candidate @relation(fields: [candidateId], references: [id])
  createdById Int
  candidateId Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime? @updatedAt
}

model Candidate {
  id          Int       @id @default(autoincrement())
  name        String
  description String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime? @updatedAt
  jobs        Job[]
}
