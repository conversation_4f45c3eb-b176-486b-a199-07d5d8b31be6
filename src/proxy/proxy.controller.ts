import { <PERSON>, <PERSON>, Req, Res, Param } from '@nestjs/common';
import { Request, Response } from 'express';
import { ProxyService } from './proxy.service';

@Controller('proxy')
export class ProxyController {
  constructor(private readonly proxyService: ProxyService) {}

  @All('*path')
  async proxyHandler(
    @Req() req: Request,
    @Res() res: Response,
    @Param('path') path: string,
  ) {
    return this.proxyService.forwardRequest(req, res);
  }
}
