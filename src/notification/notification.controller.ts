import {
  <PERSON>,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  UseGuards,
  Delete,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { GetUser } from 'src/auth/decorators/get-user.decorator';
import { UserSession } from 'src/user/dtos/user.dto';
import { NotificationService } from './notification.service';

@UseGuards(JwtAuthGuard)
@Controller('notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  getMyNotifications(
    @GetUser() user: UserSession,
    @Query('cursorId') cursorId?: string,
  ) {
    return this.notificationService.getNotificationsByUser(
      user.id,
      cursorId ? parseInt(cursorId, 10) : undefined,
    );
  }

  @Get('unread-count')
  getUnreadCount(@GetUser() user: UserSession) {
    return this.notificationService.getUnreadCount(user.id);
  }

  @Patch(':id/read')
  markAsRead(
    @Param('id', ParseIntPipe) id: number,
    @GetUser() user: UserSession,
  ) {
    return this.notificationService.markAsRead(id, user.id);
  }

  @Delete(':id')
  deleteNotification(
    @Param('id', ParseIntPipe) id: number,
    @GetUser() user: UserSession,
  ) {
    return this.notificationService.deleteNotification(id, user.id);
  }
}
