#!/bin/bash

# Simple Migration Job API Test - Updated Structure
# Quick test for the POST /jobs/migration endpoint with new payload structure

BASE_URL="http://localhost:3000"

echo "🚀 Testing Updated Migration Job API..."
echo "Endpoint: ${BASE_URL}/jobs/migration"
echo "New structure: { client: {}, job: {}, candidates: [] }"
echo ""

# Generate unique timestamp for emails and LinkedIn URLs
TIMESTAMP=$(date +%s)

# Simple test with new payload structure
curl -X POST "${BASE_URL}/jobs/migration" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"client\": {
      \"name\": \"Test Company Inc\",
      \"email\": \"test-company-${TIMESTAMP}@example.com\",
      \"industry\": \"Technology\",
      \"location\": \"San Francisco, CA\",
      \"phone\": \"******-0123\",
      \"summary\": \"A test company for migration API testing\"
    },
    \"job\": {
      \"title\": \"Software Engineer - Migration Test\",
      \"description\": \"Test job created via updated migration API\",
      \"status\": \"DRAFT\",
      \"type\": \"FULL_TIME\",
      \"workLocation\": \"HYBRID\",
      \"department\": \"Engineering\",
      \"location\": \"San Francisco, CA\",
      \"keySkills\": [\"JavaScript\", \"React\", \"Node.js\"],
      \"responsibilities\": [\"Develop applications\", \"Write tests\"],
      \"requirements\": [\"3+ years experience\", \"Bachelor degree\"],
      \"benefits\": [\"Health insurance\", \"Remote work\"],
      \"salaryMin\": 60000,
      \"salaryMax\": 90000,
      \"salaryCurrency\": \"USD\",
      \"salaryPeriod\": \"YEARLY\"
    },
    \"candidates\": [
      {
        \"firstName\": \"John\",
        \"lastName\": \"Doe\",
        \"linkedin\": \"https://linkedin.com/in/johndoe-test-${TIMESTAMP}\",
        \"email\": \"<EMAIL>\",
        \"title\": \"Software Engineer\",
        \"company\": \"Tech Corp\",
        \"location\": \"San Francisco, CA\",
        \"summary\": \"Experienced software engineer with 5 years in web development\",
        \"phone\": \"******-0456\",
        \"skills\": [\"JavaScript\", \"React\", \"Node.js\"],
        \"hourlyRate\": 65,
        \"availability\": \"2 weeks notice\",
        \"note\": \"Great candidate for this position\"
      }
    ]
  }" \
  -w "\n\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -v

echo ""
echo "✅ Test completed!"
echo ""
echo "Expected behavior:"
echo "- If client email doesn't exist: creates new client with ACTIVE status"
echo "- If client email exists: uses existing client"
echo "- Creates job linked to the client"
echo "- Processes candidates and links them to the job"
echo ""
echo "Common issues:"
echo "- 401 Unauthorized: Add authentication headers if required"
echo "- 400 Bad Request: Check payload structure and required fields"
echo "- 500 Internal Server Error: Check server logs for details"
