# Migration API Updates

## Summary of Changes

This document outlines the updates made to the migration APIs to support additional candidate fields and job-candidate relationship fields.

## 1. Database Schema Changes

### JobCandidate Model
- **Added field**: `note` (String?, optional, @db.Text)
- This field stores job-specific notes about the candidate

**Migration needed**: Run `npx prisma migrate dev --name add_note_to_job_candidate` to apply schema changes.

## 2. DTO Updates

### CreateCandidateDto
- **Added fields**:
  - `skills: string[]` (optional) - Array of candidate skills
  - `phone: string` (optional) - Candidate phone number
  - `hourlyRate: number` (optional) - Candidate hourly rate
  - `availability: string` (optional) - Candidate availability

### New DTO: CreateCandidateWithJobDto
- **Extends**: `CreateCandidateDto`
- **Additional fields**:
  - `qualified?: CandidateQualification` - Job-specific qualification status
  - `jobNote?: string` - Job-specific note (stored in JobCandidate.note)

### UpdateJobCandidateDto
- **Added field**: `note: string` (optional) - Job-candidate relationship note

## 3. API Endpoints Updated

### POST /jobs/migration
- **Purpose**: Create new job with client and candidates
- **Updated**: Now accepts `qualified` and `jobNote` fields for each candidate
- **Behavior**: 
  - Creates JobCandidate relationships with qualification and note
  - Updates existing JobCandidate if candidate already linked to job

### POST /jobs/migration/:jobId
- **Purpose**: Migrate candidates to existing job
- **Updated**: Now accepts `qualified` and `jobNote` fields for each candidate
- **Behavior**:
  - Creates new candidates or updates existing ones
  - Creates JobCandidate relationships with qualification and note
  - Updates existing JobCandidate if candidate already linked to job

### PUT /job-candidates/:jobId/:candidateId
- **Updated**: Now accepts `note` field for updating job-candidate relationship
- **Validation**: Only one field (status, qualified, or note) can be updated at a time

## 4. Request Payload Examples

### Migration Job with Candidates
```json
{
  "client": {
    "name": "Tech Corp",
    "email": "<EMAIL>",
    "industry": "Technology"
  },
  "job": {
    "title": "Senior Developer",
    "description": "Senior developer position",
    "status": "DRAFT",
    "type": "FULL_TIME",
    "workLocation": "HYBRID"
  },
  "candidates": [
    {
      "firstName": "John",
      "lastName": "Doe",
      "linkedin": "https://linkedin.com/in/johndoe",
      "email": "<EMAIL>",
      "title": "Software Engineer",
      "skills": ["JavaScript", "React", "Node.js"],
      "phone": "******-0123",
      "hourlyRate": 75,
      "availability": "Available immediately",
      "note": "General candidate note",
      "qualified": "QUALIFIED",
      "jobNote": "Perfect fit for this senior role"
    }
  ]
}
```

### Migration Candidates to Existing Job
```json
{
  "candidates": [
    {
      "firstName": "Jane",
      "lastName": "Smith",
      "linkedin": "https://linkedin.com/in/janesmith",
      "skills": ["Python", "Django", "PostgreSQL"],
      "qualified": "MAYBE",
      "jobNote": "Good technical skills but needs more experience"
    }
  ]
}
```

## 5. Field Mappings

| Candidate Field | Stored In | Purpose |
|----------------|-----------|---------|
| `firstName`, `lastName`, `email`, etc. | `Candidate` table | Basic candidate information |
| `skills`, `phone`, `hourlyRate`, `availability` | `Candidate` table | Extended candidate information |
| `note` | `CandidateNote` table | General candidate notes |
| `qualified` | `JobCandidate` table | Job-specific qualification status |
| `jobNote` | `JobCandidate` table | Job-specific notes about the candidate |

## 6. Validation Rules

- `linkedin` is required for all candidates
- `qualified` must be a valid `CandidateQualification` enum value
- `skills` must be an array of strings
- `hourlyRate` must be a number
- Only one of `status`, `qualified`, or `note` can be updated at a time in job-candidate updates

## 7. Test Files Updated

- `test-migration-job.js` - Updated with new fields
- `test-migration-candidates.js` - Updated with new fields  
- `test-migration-candidates.sh` - Updated with new fields

## 8. Next Steps

1. Run database migration: `npx prisma migrate dev --name add_note_to_job_candidate`
2. Generate Prisma client: `npx prisma generate`
3. Test the APIs using the provided test scripts
4. Update frontend to support the new fields

## 9. Breaking Changes

- None. All new fields are optional and backward compatible.

## 10. Benefits

- More comprehensive candidate data capture
- Job-specific candidate evaluation (qualified field)
- Job-specific notes separate from general candidate notes
- Better candidate-job relationship management
