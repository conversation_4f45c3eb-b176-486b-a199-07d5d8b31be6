import { Injectable, HttpStatus } from '@nestjs/common';
import { CandidateQualification, CandidateStatus } from '@prisma/client';
import { UpdateJobCandidateDto } from 'src/job-candidate/dtos/job-candidate.dto';
import { PrismaService } from 'src/prisma.service';
import { BaseResponse } from 'src/common/dto/base-response.dto';

@Injectable()
export class JobCandidateService {
  constructor(private prisma: PrismaService) {}

  async update(data: UpdateJobCandidateDto) {
    const { jobId, candidateId, status, qualified } = data;

    const existing = await this.prisma.jobCandidate.findUnique({
      where: {
        jobId_candidateId: {
          jobId,
          candidateId,
        },
      },
    });

    if (!existing) {
      throw new Error('JobCandidate record not found.');
    }

    if (status !== undefined && qualified !== undefined) {
      throw new Error(
        'Only one of "status" or "qualified" can be updated at a time.',
      );
    }

    const updateData: Partial<{
      status: CandidateStatus;
      qualified: CandidateQualification;
    }> = {};

    if (status !== undefined) {
      updateData.status = status;
    } else if (qualified !== undefined) {
      updateData.qualified = qualified;
    } else {
      throw new Error(
        'No valid field provided for update. Require status or qualified field.',
      );
    }

    const updated = await this.prisma.jobCandidate.update({
      where: {
        jobId_candidateId: {
          jobId,
          candidateId,
        },
      },
      data: updateData,
    });

    return new BaseResponse({
      data: updated,
      message: 'JobCandidate updated successfully',
      statusCode: HttpStatus.OK,
    });
  }
}
