import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { GetUser } from 'src/auth/decorators/get-user.decorator';
import { UserSession } from 'src/user/dtos/user.dto';
import { UserActivityService } from './user-activity.service';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { ActivityAction, ActivityObject, UserActivity } from '@prisma/client';

interface LogActivityDto {
  action: ActivityAction;
  objectType: ActivityObject;
  objectId: string;
  metadata?: Record<string, any>;
}

@UseGuards(JwtAuthGuard)
@Controller('user-activities')
export class UserActivityController {
  constructor(private readonly userActivityService: UserActivityService) {}

  @Get()
  async getMyActivities(
    @GetUser() user: UserSession,
  ): Promise<BaseResponse<UserActivity[] | null>> {
    return this.userActivityService.getActivitiesByUser(user.id);
  }

  @Post()
  async logActivity(
    @GetUser() user: UserSession,
    @Body() body: LogActivityDto,
  ): Promise<BaseResponse<UserActivity | null>> {
    return this.userActivityService.logActivity({
      userId: user.id,
      ...body,
    });
  }
}
