import { IsEmail, <PERSON>NotEmpt<PERSON>, <PERSON><PERSON>ption<PERSON>, IsString } from 'class-validator';

export class CreateCustomerDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsOptional()
  name: string;

  @IsString()
  @IsNotEmpty()
  company: string;
}

export class AttachPaymentMethodDto {
  @IsString()
  paymentMethodId: string;

  @IsString()
  customerId: string;
}

export class CreateSubscriptionDto {
  @IsString()
  @IsNotEmpty()
  customerId: string;

  @IsOptional()
  @IsString()
  plan?: 'starter' | 'pro';
}
