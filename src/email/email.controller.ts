import { Body, Controller, Post } from '@nestjs/common';
import { EmailService } from './email.service';

@Controller('email')
export class EmailController {
  constructor(private readonly emailService: EmailService) {}

  @Post('public/notify-new-agency')
  async notifyNewAgency(
    @Body() body: { email: string; companyName: string },
  ): Promise<{ message: string }> {
    console.log('Notify new agency email api =>', body);
    await this.emailService.sendNewAgencyNotification(
      body.email,
      body.companyName,
    );
    return { message: 'Notification email sent successfully' };
  }

  @Post('public/notify-request-upgrade')
  async requestUpgrade(
    @Body()
    body: {
      email: string;
      companyName: string;
      plan: 'trial' | 'starter' | 'pro';
    },
  ): Promise<{ message: string }> {
    console.log('Upgrade request email api =>', body);
    await this.emailService.sendUpgradeRequestNotification(
      body.email,
      body.companyName,
      body.plan,
    );
    return { message: 'Upgrade request email sent successfully' };
  }

  @Post('public/notify-request-enterprise')
  async notifyRequestEnterprise(
    @Body()
    body: {
      name: string;
      email: string;
      phone: string;
      company: string;
      expectedCandidatesPerWeek: string;
      expectedRolesPerMonth: string;
    },
  ): Promise<{ message: string }> {
    console.log('Enterprise contact form email api =>', body);
    await this.emailService.sendEnterpriseContactNotification(
      body.name,
      body.email,
      body.phone,
      body.company,
      body.expectedCandidatesPerWeek,
      body.expectedRolesPerMonth,
    );
    return { message: 'Enterprise contact email sent successfully' };
  }
}
