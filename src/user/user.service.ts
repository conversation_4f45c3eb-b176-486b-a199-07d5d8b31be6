import { Request } from 'express';
import { Injectable, HttpStatus } from '@nestjs/common';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { User } from 'src/user/user.interface';
import { UpdateUserDto } from 'src/user/dtos/user.dto';
import { getClientHostFromRequest } from 'src/utils/request.util';
import { getMasterApiUrl, getMasterApiKey } from 'src/configs/config';

@Injectable()
export class UserService {
  constructor() {}

  async getDetail(
    req: Request,
    id: string,
  ): Promise<BaseResponse<User | null>> {
    const cookieHeader = req.headers.cookie;

    try {
      const response = await fetch(`${getMasterApiUrl()}/user/${id}`, {
        method: 'GET',
        headers: {
          Cookie: cookieHeader,
          'Content-Type': 'application/json',
          'x-api-key': getMasterApi<PERSON>ey(),
          'x-client-host': getClientHostFromRequest(req),
        } as HeadersInit,
      });

      const result = await response.json();

      if (!response.ok) {
        return new BaseResponse({
          data: null,
          message: result?.message || 'Failed to get user detail.',
          statusCode: response.status,
        });
      }

      return new BaseResponse({
        data: result.data,
        message: 'Get user successfully.',
        statusCode: response.status,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getActiveUsers(req: Request): Promise<BaseResponse<User[] | null>> {
    try {
      const cookieHeader = req.headers.cookie;
      const response = await fetch(`${getMasterApiUrl()}/user/list-active`, {
        method: 'GET',
        headers: {
          Cookie: cookieHeader,
          'Content-Type': 'application/json',
          'x-api-key': getMasterApiKey(),
          'x-client-host': getClientHostFromRequest(req),
        } as HeadersInit,
      });

      const result = await response.json();

      if (!response.ok) {
        return new BaseResponse({
          data: null,
          message: result?.message || 'Failed to get active users.',
          statusCode: response.status,
        });
      }

      return new BaseResponse({
        data: result.data,
        message: 'Get active users successfully.',
        statusCode: response.status,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getProfile(req: Request): Promise<BaseResponse<User | null>> {
    try {
      const cookieHeader = req.headers.cookie || '';
      const response = await fetch(`${getMasterApiUrl()}/user/profile`, {
        method: 'GET',
        headers: {
          Cookie: cookieHeader,
          'Content-Type': 'application/json',
          'x-api-key': getMasterApiKey(),
          'x-client-host': getClientHostFromRequest(req),
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return new BaseResponse({
          data: null,
          message: result?.message || 'Failed to get profile.',
          statusCode: response.status,
        });
      }

      return new BaseResponse({
        data: result.data,
        message: 'Get profile successfully.',
        statusCode: response.status,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async updateProfile(
    data: UpdateUserDto,
    req: Request,
  ): Promise<BaseResponse<User | null>> {
    try {
      const cookieHeader = req.headers.cookie || '';
      const response = await fetch(`${getMasterApiUrl()}/user/profile`, {
        method: 'PUT',
        headers: {
          Cookie: cookieHeader,
          'Content-Type': 'application/json',
          'x-api-key': getMasterApiKey(),
          'x-client-host': getClientHostFromRequest(req),
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        return new BaseResponse({
          data: null,
          message: result?.message || 'Failed to update profile.',
          statusCode: response.status,
        });
      }

      return new BaseResponse({
        data: result.data,
        message: 'Profile updated successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }
}
