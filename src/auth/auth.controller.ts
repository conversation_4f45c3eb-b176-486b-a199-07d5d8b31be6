import {
  Body,
  Controller,
  Get,
  Post,
  Res,
  Req,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ChangePasswordDto, LoginDto } from './dtos/auth.dto';
import { AuthService } from 'src/auth/auth.service';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { GetUser } from 'src/auth/decorators/get-user.decorator';
import { UserSession } from 'src/user/dtos/user.dto';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('login')
  async login(
    @Req() req: Request,
    @Body() body: LoginDto,
    @Res({ passthrough: true }) res: Response,
  ): Promise<
    BaseResponse<{ accessToken: string; refreshToken: string } | null>
  > {
    const result = await this.authService.login(req, body);

    if (!result.data) {
      return result;
    }

    const { accessToken, refreshToken } = result.data;

    res.cookie('accessToken', accessToken, {
      httpOnly: true,
      secure: true,
      sameSite: 'none', // TODO, use 'lax' if API and FE are hosted on the same domain
      maxAge: 1000 * 60 * 60 * 24 * 7, // 7 days
    });

    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: true,
      sameSite: 'none', // TODO, use 'lax' if API and FE are hosted on the same domain
      maxAge: 1000 * 60 * 60 * 24 * 30, // 30 days
    });

    return result;
  }

  @Post('logout')
  logout(@Res() res: Response): any {
    res.clearCookie('accessToken', {
      httpOnly: true,
      secure: true,
      sameSite: 'none', // TODO, use 'lax' if API and FE are hosted on the same domain
    });

    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: true,
      sameSite: 'none', // TODO, use 'lax' if API and FE are hosted on the same domain
    });

    return res.status(HttpStatus.OK).json({
      statusCode: HttpStatus.OK,
      message: 'Logout successful',
      data: null,
    });
  }

  @Get('refresh')
  async refreshToken(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
  ): Promise<BaseResponse<{ accessToken: string } | null>> {
    const refreshToken = req.cookies.refreshToken;

    if (!refreshToken) {
      return {
        data: null,
        message: 'No refresh token found',
        statusCode: HttpStatus.UNAUTHORIZED,
      };
    }

    try {
      const result = await this.authService.refreshAccessToken(
        req,
        refreshToken,
      );
      const newAccessToken = result?.data?.accessToken || '';

      res.cookie('accessToken', result?.data?.accessToken, {
        httpOnly: true,
        secure: true,
        sameSite: 'none', // TODO, use 'lax' if API and FE are hosted on the same domain
        maxAge: 1000 * 60 * 60 * 24 * 7, // 7 days
      });

      return {
        data: { accessToken: newAccessToken },
        message: 'Token refreshed successfully',
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      return {
        data: null,
        message: 'Invalid refresh token',
        statusCode: HttpStatus.FORBIDDEN,
      };
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  async getMe(@Req() req: Request): Promise<BaseResponse<UserSession | null>> {
    return this.authService.getMe(req);
  }

  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  async changePassword(
    @GetUser() user: UserSession,
    @Body() body: ChangePasswordDto,
    @Req() req: Request,
  ): Promise<BaseResponse<null>> {
    const result = await this.authService.changePassword(
      user.id,
      body.oldPassword,
      body.newPassword,
      req,
    );

    return new BaseResponse({
      statusCode: result.statusCode,
      message: result.message,
      data: result.data,
    });
  }
}
