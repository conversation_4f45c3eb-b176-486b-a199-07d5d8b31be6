import {
  Req,
  Controller,
  Get,
  Param,
  Post,
  Body,
  Query,
  ParseIntPipe,
  Put,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { Candidate } from '@prisma/client';
import { Request } from 'express';
import { CandidateService } from 'src/candidate/candidate.service';
import {
  CandidatePaginationResponseType,
  CreateCandidateDto,
  CandidateFilterType,
  UpdateCandidateDto,
  AddCandidateNoteDto,
} from 'src/candidate/dtos/candidate.dto';
import { GetUser } from 'src/auth/decorators/get-user.decorator';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { UserSession } from 'src/user/dtos/user.dto';

@Controller('candidates')
export class CandidateController {
  constructor(private candidateService: CandidateService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  create(
    @Body() data: CreateCandidateDto,
    @GetUser() user: UserSession,
  ): Promise<BaseResponse<Candidate | null>> {
    return this.candidateService.create(data, user);
  }

  @Post('import')
  @UseGuards(JwtAuthGuard)
  async importCandidates(
    @Req() req: Request,
    @Body() data: { candidates: CreateCandidateDto[]; jobId: number },
    @GetUser() user: UserSession,
  ): Promise<
    BaseResponse<{
      imported: Candidate[];
      failed: { candidate: CreateCandidateDto; reason: string }[];
    }>
  > {
    console.log(
      'Import candidates API',
      data.candidates.length,
      'Job ID:',
      data.jobId,
    );
    return this.candidateService.importCandidates(
      req,
      data.candidates,
      data.jobId,
      user,
    );
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  getAll(
    @Query() params: CandidateFilterType,
  ): Promise<BaseResponse<CandidatePaginationResponseType | null>> {
    console.log('Get all candidate api', params);
    return this.candidateService.getAll(params);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  getDetail(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BaseResponse<Candidate | null>> {
    console.log('Get candidate detail api', id);
    return this.candidateService.getDetail(req, id);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateCandidateDto,
  ): Promise<BaseResponse<Candidate | null>> {
    console.log('Update candidate api', id);
    return this.candidateService.update(id, data);
  }

  @Post(':id/apply-jobs')
  @UseGuards(JwtAuthGuard)
  applyJob(
    @Req() req: Request,
    @Param('id', ParseIntPipe) candidateId: number,
    @Body() { jobIds }: { jobIds: number[] },
    @GetUser() user: UserSession,
  ) {
    console.log(
      'Candidate apply job',
      `id: ${candidateId}, jobId: ${jobIds.toString()}`,
    );
    return this.candidateService.applyJobs(req, candidateId, jobIds, user);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  delete(@Param('id', ParseIntPipe) id: number) {
    console.log('Delete candidate', id);
    return this.candidateService.delete(id);
  }

  @Post(':id/notes')
  @UseGuards(JwtAuthGuard)
  addNote(
    @Param('id', ParseIntPipe) candidateId: number,
    @Body() data: AddCandidateNoteDto,
    @GetUser() user: UserSession,
  ): Promise<BaseResponse<any>> {
    console.log('Add note to candidate', candidateId, data.content);
    return this.candidateService.addNote(candidateId, data.content, user);
  }
}
