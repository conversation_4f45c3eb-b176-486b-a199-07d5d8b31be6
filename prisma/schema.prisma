// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  TEAM
}

enum CountryCode {
  US
  CA
  UK
  AU
  DE
  FR
  JP
}

enum Timezone {
  PACIFIC // America/Los_Angeles
  MOUNTAIN // America/Denver
  CENTRAL // America/Chicago
  EASTERN // America/New_York
  GMT // Etc/UTC
  CET // Europe/Berlin
  AEST // Australia/Sydney
}

enum JobType {
  FULL_TIME
  PART_TIME
  CONTRACT
  TEMPORARY
  INTERNSHIP
}

enum WorkLocation {
  FULLY_ONSITE
  HYBRID
  REMOTE
}

enum JobStatus {
  DRAFT
  ACTIVE
  PAUSED
  CLOSED
}

enum ClientStatus {
  ACTIVE
  INACTIVE
  PROSPECT
}

enum CandidateStatus {
  PROSPECT
  MATCHED
  CONTACTED
  INTERVIEWING
  TECHNICAL_TEST
  REFERENCE_CHECK
  OFFER
  HIRED
  REJECTED
  ON_HOLD
}

enum CandidateQualification {
  QUALIFIED
  NOT_QUALIFIED
}

enum SalaryCurrency {
  USD
  EUR
  GBP
  CAD
  AUD
  JPY
  CNY
  INR
}

enum SalaryPeriod {
  HOURLY
  MONTHLY
  YEARLY
}

enum ActivityAction {
  CREATE
  UPDATE
  DELETE
  APPLY
  LOGIN
  LOGOUT
}

enum ActivityObject {
  USER
  CLIENT
  JOB
  CANDIDATE
  JOB_CANDIDATE
  NOTIFICATION
  CANDIDATE_NOTE
}

enum NotificationType {
  CANDIDATE_APPLIED
  JOB_CREATED
  JOB_UPDATED
  SYSTEM
  GENERAL
}

model Client {
  id       Int          @id @default(autoincrement())
  name     String
  email    String       @unique
  industry String
  phone    String?
  location String?
  summary  String?      @db.Text
  website  String?
  status   ClientStatus @default(PROSPECT)

  createdById String

  jobs      Job[]
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

model Job {
  id               Int            @id @default(autoincrement())
  title            String
  description      String         @db.Text
  client           Client         @relation(fields: [clientId], references: [id])
  clientId         Int
  department       String?
  location         String?
  type             JobType        @default(FULL_TIME)
  workLocation     WorkLocation   @default(FULLY_ONSITE)
  keySkills        String[]
  responsibilities String[]
  requirements     String[]
  benefits         String[]
  salaryMin        Int?           @default(0)
  salaryMax        Int?           @default(0)
  salaryCurrency   SalaryCurrency @default(USD)
  salaryPeriod     SalaryPeriod   @default(MONTHLY)
  status           JobStatus      @default(DRAFT)
  startDate        DateTime?
  endDate          DateTime?
  activatedAt      DateTime?

  candidates JobCandidate[]

  createdById String

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

model Candidate {
  id           Int      @id @default(autoincrement())
  firstName    String?
  lastName     String?
  email        String?
  linkedin     String   @unique
  title        String?
  company      String?
  location     String?
  summary      String?  @db.Text
  phone        String?
  skills       String[]
  hourlyRate   Int?
  availability String?

  createdById String

  jobs  JobCandidate[]
  notes CandidateNote[]

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

model CandidateNote {
  id          Int       @id @default(autoincrement())
  content     String    @db.Text
  candidate   Candidate @relation(fields: [candidateId], references: [id])
  candidateId Int
  createdById String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime? @updatedAt
}

model JobCandidate {
  id          Int                     @id @default(autoincrement())
  job         Job                     @relation(fields: [jobId], references: [id])
  jobId       Int
  candidate   Candidate               @relation(fields: [candidateId], references: [id])
  candidateId Int
  status      CandidateStatus         @default(PROSPECT)
  qualified   CandidateQualification?
  createdById String
  createdAt   DateTime                @default(now())
  updatedAt   DateTime?               @updatedAt

  @@unique([jobId, candidateId])
}

model UserActivity {
  id         Int            @id @default(autoincrement())
  userId     String
  action     ActivityAction
  objectType ActivityObject
  objectId   String
  metadata   Json?
  createdAt  DateTime       @default(now())
}

model Notification {
  id        Int              @id @default(autoincrement())
  userId    String
  title     String
  content   String?
  metadata  Json?
  type      NotificationType @default(GENERAL)
  read      Boolean          @default(false)
  createdAt DateTime         @default(now())
}
