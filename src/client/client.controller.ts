import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Client, ClientStatus } from '@prisma/client';
import {
  CreateClientDto,
  ClientFilterType,
  ClientPaginationResponseType,
  UpdateClientDto,
} from '../client/dtos/client.dto';
import { ClientService } from '../client/client.service';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { GetUser } from 'src/auth/decorators/get-user.decorator';
import { UserSession } from 'src/user/dtos/user.dto';

@UseGuards(JwtAuthGuard)
@Controller('clients')
export class ClientController {
  constructor(private clientService: ClientService) {}

  @Post()
  create(
    @Body() body: CreateClientDto,
    @GetUser() user: UserSession,
  ): Promise<BaseResponse<Client | null>> {
    console.log('Create client api =>', body);
    return this.clientService.create(body, user);
  }

  @Get()
  getAll(
    @Query() params: ClientFilterType,
  ): Promise<BaseResponse<ClientPaginationResponseType | null>> {
    console.log('Get all client api', params);
    return this.clientService.getAll(params);
  }

  @Get('basic')
  getAllBasic(@Query('status') status?: ClientStatus) {
    console.log('Get all basic client api');
    return this.clientService.getAllBasic(status);
  }

  @Get(':id')
  getDetail(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BaseResponse<Client | null>> {
    console.log('Get client detail api', id);
    return this.clientService.getDetail(id);
  }

  @Put(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateClientDto,
    @GetUser() user: UserSession,
  ): Promise<BaseResponse<Client | null>> {
    console.log('Update client api', id);
    return this.clientService.update(id, data, user);
  }

  @Delete(':id')
  delete(@Param('id', ParseIntPipe) id: number, @GetUser() user: UserSession) {
    console.log('Delete client', id);
    return this.clientService.delete(id, user);
  }
}
