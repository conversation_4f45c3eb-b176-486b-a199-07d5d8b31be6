/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { HttpStatus, Injectable } from '@nestjs/common';
import {
  CandidateQualification,
  CandidateStatus,
  JobStatus,
} from '@prisma/client';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { RecruitStatus } from 'src/common/enum/dashboard.enum';
import { getDateRange } from 'src/common/utils/dateRange.util';
import {
  DashboardFilterType,
  DashboardSummary,
} from 'src/dashboard/dtos/dashboard.dto';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class DashboardService {
  constructor(private prisma: PrismaService) {}

  async getOverview(
    filters: DashboardFilterType,
  ): Promise<BaseResponse<DashboardSummary>> {
    const {
      filterBy,
      page = 1,
      items_per_page = 10,
      search,
      status,
      sortField,
      sortDirection = 'asc',
    } = filters;
    const range = getDateRange(filterBy);
    const prevRange = range
      ? {
          from: new Date(
            range.from.getTime() - (range.to.getTime() - range.from.getTime()),
          ),
          to: range.from,
        }
      : null;

    const [currentJobs, prevJobs, currentCandidates, prevCandidates] =
      await Promise.all([
        this.prisma.job.count({
          where: {
            status: JobStatus.ACTIVE,
            createdAt: range ? { gte: range.from, lte: range.to } : undefined,
          },
        }),
        this.prisma.job.count({
          where: {
            status: JobStatus.ACTIVE,
            createdAt: prevRange
              ? { gte: prevRange.from, lte: prevRange.to }
              : undefined,
          },
        }),
        this.prisma.candidate.count({
          where: range
            ? { createdAt: { gte: range.from, lte: range.to } }
            : undefined,
        }),
        this.prisma.candidate.count({
          where: prevRange
            ? { createdAt: { gte: prevRange.from, lte: prevRange.to } }
            : undefined,
        }),
      ]);

    const jobStats = await this.prisma.job.findMany({
      where: {
        startDate: range ? { gte: range.from, lte: range.to } : undefined,
        status: JobStatus.ACTIVE,
      },
      include: {
        client: true,
        candidates: true,
      },
    });

    const fullJobSummaries = jobStats.map((job) => {
      const candidates = job.candidates;
      const prospects = candidates.length;

      const outreaches = candidates.filter(
        (jc) =>
          jc.status !== CandidateStatus.PROSPECT &&
          jc.status !== CandidateStatus.MATCHED &&
          jc.qualified !== CandidateQualification.QUALIFIED,
      ).length;

      const replied = candidates.filter(
        (jc) =>
          jc.status !== CandidateStatus.PROSPECT &&
          jc.status !== CandidateStatus.MATCHED &&
          jc.qualified !== CandidateQualification.QUALIFIED &&
          jc.status !== CandidateStatus.CONTACTED,
      ).length;

      const qualified = candidates.filter(
        (jc) =>
          jc.status !== CandidateStatus.PROSPECT &&
          jc.status !== CandidateStatus.MATCHED &&
          jc.status !== CandidateStatus.REJECTED,
      ).length;

      let jobStatus: RecruitStatus;
      if (prospects === 0 || outreaches === 0) {
        jobStatus = RecruitStatus.NO_PROSPECTS;
      } else {
        const qualifiedRate = (qualified / outreaches) * 100;

        if (qualifiedRate >= 70) {
          jobStatus = RecruitStatus.HEALTHY;
        } else if (qualifiedRate >= 40) {
          jobStatus = RecruitStatus.AT_RISK;
        } else {
          jobStatus = RecruitStatus.CRITICAL;
        }
      }

      return {
        jobId: job.id,
        clientId: job.clientId,
        clientName: job.client.name,
        jobTitle: job.title,
        jobStatus,
        started: job.activatedAt,
        prospects,
        outreaches,
        replied,
        qualified,
      };
    });

    let filteredJobSummaries = fullJobSummaries;

    if (search) {
      const keyword = search.toLowerCase();
      filteredJobSummaries = filteredJobSummaries.filter(
        (j) =>
          j.clientName.toLowerCase().includes(keyword) ||
          j.jobTitle.toLowerCase().includes(keyword),
      );
    }

    if (status) {
      filteredJobSummaries = filteredJobSummaries.filter(
        (j) => j.jobStatus === status,
      );
    }

    if (sortField) {
      filteredJobSummaries.sort((a, b) => {
        let aValue: any = a[sortField as keyof typeof a];
        let bValue: any = b[sortField as keyof typeof b];

        // Special case for jobStatus/status to compare string values
        if (sortField === 'jobStatus' || sortField === 'status') {
          aValue = String(aValue);
          bValue = String(bValue);
        }

        // Handle undefined or null values
        if (aValue === undefined || aValue === null) aValue = '';
        if (bValue === undefined || bValue === null) bValue = '';

        // Compare dates
        if (aValue instanceof Date && bValue instanceof Date) {
          if (aValue.getTime() < bValue.getTime())
            return sortDirection === 'asc' ? -1 : 1;
          if (aValue.getTime() > bValue.getTime())
            return sortDirection === 'asc' ? 1 : -1;
          return 0;
        }

        // Compare numbers
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
        }

        // Compare strings
        aValue = String(aValue).toLowerCase();
        bValue = String(bValue).toLowerCase();
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    const totalJobs = filteredJobSummaries.length;
    const jobSummaries = filteredJobSummaries.slice(
      (page - 1) * items_per_page,
      page * items_per_page,
    );

    const data: DashboardSummary = {
      filterBy,
      totalActiveJobs: {
        current: currentJobs,
        change: currentJobs - prevJobs,
      },
      totalCandidates: {
        current: currentCandidates,
        change: currentCandidates - prevCandidates,
      },
      totalJobs,
      jobSummaries,
    };

    const response: BaseResponse<DashboardSummary> = {
      statusCode: HttpStatus.OK,
      message: 'Dashboard data fetched successfully',
      data,
    };

    return response;
  }
}
