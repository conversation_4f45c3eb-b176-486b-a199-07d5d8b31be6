import { NestFactory } from '@nestjs/core';
import * as cookieParser from 'cookie-parser';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.use(cookieParser());

  app.enableCors({
    origin: [
      'http://localhost:8080', // Recruit Dashboard
      'http://localhost:8081', // DASH Intake
      'https://dash.talentlabsai.com', // DASH Intake Prod
      'https://dash-intake-platform.vercel.app', // DASH Intake Prod

      // DEV Env - Vercel Prod Plan - dev branch on GIT (same instance with prod)
      'https://recruit-dashboard-dev.talentlabsai.com',

      // PROD Env - Vercel Prod Plan - main branch on GIT (1 instance only, different APIs)
      'https://recruit-dashboard-prod.vercel.app',
      'https://demo-dashboard.talentlabsai.com', // Demo - <EMAIL>
      'https://fishercat.talentlabsai.com', // Fishercat - <EMAIL>
      'https://flyhound.talentlabsai.com', // Flyhound - <EMAIL>, <EMAIL>
    ],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
  });

  await app.listen(3000);
}

bootstrap().catch((err) => {
  console.error('Error during application bootstrap:', err);
});
