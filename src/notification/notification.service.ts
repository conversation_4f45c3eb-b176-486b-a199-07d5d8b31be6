import { HttpStatus, Injectable } from '@nestjs/common';
import { Request } from 'express';
import { PrismaService } from 'src/prisma.service';
import { NotificationType } from '@prisma/client';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { UserService } from 'src/user/user.service';

interface CreateNotificationDto {
  excludeUserId?: string;
  title: string;
  content?: string;
  type?: NotificationType | keyof typeof NotificationType;
  metadata?: Record<string, any>;
}

@Injectable()
export class NotificationService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly userService: UserService,
  ) {}

  async broadcast(
    req: Request,
    params: CreateNotificationDto,
  ): Promise<BaseResponse<null>> {
    try {
      const resp = await this.userService.getActiveUsers(req);
      const { data: users = [], statusCode, message } = resp || {};

      if (
        statusCode !== HttpStatus.OK ||
        users?.length === 0 ||
        users === null
      ) {
        return new BaseResponse({
          data: null,
          message: message || 'No recipients found.',
          statusCode: HttpStatus.OK,
        });
      }

      const filteredUsers = params.excludeUserId
        ? users?.filter((u) => u.id !== params.excludeUserId)
        : users;

      await this.prisma.notification.createMany({
        data: filteredUsers.map((user) => ({
          userId: user.id,
          title: params.title,
          content: params.content,
          type: params.type || NotificationType.GENERAL,
          metadata: params.metadata || {},
        })),
      });

      return new BaseResponse({
        data: null,
        message: 'Notifications sent.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getNotificationsByUser(
    userId: string,
    cursorId?: number,
  ): Promise<BaseResponse<any>> {
    try {
      const data = await this.prisma.notification.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 50,
        ...(cursorId && {
          skip: 1,
          cursor: { id: cursorId },
        }),
        // TODO, Master API integration
        // include: {
        //   user: {
        //     select: {
        //       id: true,
        //       firstName: true,
        //       lastName: true,
        //       email: true,
        //     },
        //   },
        // },
      });
      return new BaseResponse({
        data,
        message: 'Notifications fetched successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getUnreadCount(userId: string): Promise<BaseResponse<number>> {
    try {
      const count = await this.prisma.notification.count({
        where: { userId, read: false },
      });
      return new BaseResponse({
        data: count,
        message: 'Unread count retrieved.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: 0,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async markAsRead(id: number, userId: string): Promise<BaseResponse<null>> {
    try {
      const result = await this.prisma.notification.updateMany({
        where: { id, userId },
        data: { read: true },
      });

      if (result.count === 0) {
        return new BaseResponse({
          data: null,
          message: 'Notification not found or already read.',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      return new BaseResponse({
        data: null,
        message: 'Notification marked as read.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async deleteNotification(
    id: number,
    userId: string,
  ): Promise<BaseResponse<null>> {
    try {
      const deleted = await this.prisma.notification.deleteMany({
        where: { id, userId },
      });

      if (deleted.count === 0) {
        return new BaseResponse({
          data: null,
          message: 'Notification not found or already deleted.',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      return new BaseResponse({
        data: null,
        message: 'Notification deleted successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error: any) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }
}
