import { Injectable, HttpStatus } from '@nestjs/common';
import { Prisma, Candidate } from '@prisma/client';
import { Request } from 'express';
import { NotificationService } from 'src/notification/notification.service';
import { UserActivityService } from 'src/user-activity/user-activity.service';
import {
  NotificationType,
  ActivityAction,
  ActivityObject,
} from '@prisma/client';
import {
  CandidatePaginationResponseType,
  CandidateFilterType,
  CreateCandidateDto,
  UpdateCandidateDto,
} from 'src/candidate/dtos/candidate.dto';
import { PrismaService } from 'src/prisma.service';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { UserSession } from 'src/user/dtos/user.dto';
import { UserService } from '../user/user.service';

@Injectable()
export class CandidateService {
  constructor(
    private readonly userService: UserService,
    private prismaService: PrismaService,
    private notificationService: NotificationService,
    private userActivityService: UserActivityService,
  ) {}

  async create(
    body: CreateCandidateDto,
    loggedUser: UserSession,
  ): Promise<BaseResponse<Candidate | null>> {
    const { jobId, ...candidateData } = body;

    try {
      const candidateWithJobs = await this.prismaService.$transaction(
        async (tx) => {
          const newCandidate = await tx.candidate.create({
            data: {
              ...candidateData,
              createdById: loggedUser.id,
            },
          });

          if (jobId) {
            await tx.jobCandidate.create({
              data: {
                jobId: jobId,
                candidateId: newCandidate.id,
                createdById: loggedUser.id,
              },
            });
          }

          const candidate = await tx.candidate.findUnique({
            where: { id: newCandidate.id },
            include: {
              jobs: {
                include: {
                  job: true,
                },
              },
            },
          });

          return candidate;
        },
      );

      if (!candidateWithJobs) {
        return new BaseResponse({
          data: null,
          message: 'Candidate creation failed or job not found.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        });
      }

      return new BaseResponse({
        data: candidateWithJobs,
        message: 'Candidate created successfully.',
        statusCode: HttpStatus.CREATED,
      });
    } catch (error: any) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      if (error.code === 'P2003') {
        // Prisma error code for Foreign Key violation
        return new BaseResponse({
          data: null,
          message: 'Job not found. Invalid jobId provided.',
          statusCode: HttpStatus.BAD_REQUEST,
        });
      }

      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getAll(
    filters: CandidateFilterType,
  ): Promise<BaseResponse<CandidatePaginationResponseType | null>> {
    try {
      const items_per_page = Number(filters.items_per_page) || 10;
      const page = Number(filters.page) || 1;
      const search = filters.search?.trim().toLowerCase() || '';
      const skip = page > 1 ? (page - 1) * items_per_page : 0;

      const whereClause = {
        OR: [
          {
            firstName: { contains: search, mode: Prisma.QueryMode.insensitive },
          },
          {
            lastName: { contains: search, mode: Prisma.QueryMode.insensitive },
          },
          {
            linkedin: { contains: search, mode: Prisma.QueryMode.insensitive },
          },
          { title: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { summary: { contains: search, mode: Prisma.QueryMode.insensitive } },
        ],
      };

      const [candidates, total] = await this.prismaService.$transaction([
        this.prismaService.candidate.findMany({
          take: items_per_page,
          skip,
          where: whereClause,
          include: {
            jobs: {
              include: {
                job: true,
              },
            },
            notes: {
              orderBy: {
                createdAt: 'desc',
              },
              take: 5, // Get last 5 notes
              // TODO, Master API integration
              // include: {
              //   createdBy: true,
              // },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        }),
        this.prismaService.candidate.count({
          where: whereClause,
        }),
      ]);

      return new BaseResponse({
        data: {
          data: candidates,
          total,
          currentPage: page,
          itemsPerPage: items_per_page,
        },
        message: 'Get all candidates successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async getDetail(
    req: Request,
    id: number,
  ): Promise<BaseResponse<Candidate | null>> {
    try {
      const candidate = await this.prismaService.candidate.findFirst({
        where: { id },
        include: {
          jobs: {
            include: {
              job: {
                include: {
                  client: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 10,
          },
          notes: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 5, // Get last 5 notes
          },
        },
      });

      if (candidate?.notes?.length) {
        for (const note of candidate.notes) {
          if (note.createdById) {
            const resp = await this.userService.getDetail(
              req,
              note.createdById,
            );
            if (resp?.data) {
              note['createdBy'] = {
                id: resp.data.id,
                email: resp.data.email,
                firstName: resp.data?.firstName || '',
                lastName: resp.data?.lastName || '',
                phone: resp.data?.phone || '',
              };
            }
          }
        }
      }

      return {
        statusCode: 200,
        message: 'Get candidate detail success',
        data: candidate,
      };
    } catch (error) {
      console.error('Error get candidate detail', error);
      return {
        statusCode: 500,
        message: error.message,
        data: null,
      };
    }
  }

  async update(
    id: number,
    data: Partial<UpdateCandidateDto>,
  ): Promise<BaseResponse<Candidate | null>> {
    try {
      const candidate = await this.prismaService.candidate.findFirst({
        where: { id },
      });

      if (!candidate) {
        return new BaseResponse({
          data: null,
          message: `Candidate with ID ${id} not found.`,
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      const updatedCandidate = await this.prismaService.candidate.update({
        where: { id },
        data,
      });

      return new BaseResponse({
        data: updatedCandidate,
        message: 'Candidate updated successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async applyJobs(
    req: Request,
    candidateId: number,
    jobIds: number[],
    loggedUser: UserSession,
  ): Promise<BaseResponse<Candidate | null>> {
    try {
      const result = await this.prismaService.$transaction(async (tx) => {
        const candidate = await tx.candidate.findUnique({
          where: { id: candidateId },
        });

        if (!candidate) {
          return null;
        }

        const validJobs = await tx.job.findMany({
          where: {
            id: { in: jobIds },
          },
        });

        const validJobIds = validJobs.map((job) => job.id);

        if (validJobIds.length !== jobIds.length) {
          throw new Error('One or more job IDs are invalid.');
        }

        for (const jobId of validJobIds) {
          const jobCandidate = await tx.jobCandidate.upsert({
            where: {
              jobId_candidateId: { jobId, candidateId },
            },
            create: {
              jobId,
              candidateId,
              createdById: loggedUser.id,
            },
            update: {},
          });

          const metadata = {
            jobId,
            candidateId,
            jobTitle: validJobs.find((job) => job.id === jobId)?.title || '',
            candidateName: `${candidate.firstName} ${candidate.lastName}`,
          };

          await this.userActivityService.logActivity({
            userId: loggedUser.id,
            action: ActivityAction.APPLY,
            objectType: ActivityObject.JOB_CANDIDATE,
            objectId: `${jobCandidate.id}`,
            metadata,
          });

          const job = validJobs.find((j) => j.id === jobId);

          await this.notificationService.broadcast(req, {
            title: 'Candidate applied to job',
            content: `Candidate ${candidate.firstName || ''} ${
              candidate.lastName || ''
            } applied to job "${job?.title}"`,
            type: NotificationType.CANDIDATE_APPLIED,
            // excludeUserId: loggedUser.id, // TODO, maybe use later
            metadata,
          });
        }

        return await tx.candidate.findUnique({
          where: { id: candidateId },
          include: {
            jobs: {
              include: {
                job: true,
              },
            },
          },
        });
      });

      if (!result) {
        return new BaseResponse({
          data: null,
          message: 'Candidate not found.',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      return new BaseResponse({
        data: result,
        message: 'Jobs applied successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async delete(candidateId: number): Promise<BaseResponse<null>> {
    try {
      const result = await this.prismaService.$transaction(async (tx) => {
        const candidate = await tx.candidate.findUnique({
          where: { id: candidateId },
        });

        if (!candidate) {
          return null;
        }

        await tx.jobCandidate.deleteMany({
          where: { candidateId },
        });

        await tx.candidate.delete({
          where: { id: candidateId },
        });

        return true;
      });

      if (!result) {
        return new BaseResponse({
          data: null,
          message: 'Candidate not found.',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      return new BaseResponse({
        data: null,
        message: 'Candidate deleted successfully.',
        statusCode: HttpStatus.OK,
      });
    } catch (error) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  async importCandidates(
    req: Request,
    candidates: CreateCandidateDto[],
    jobId: number,
    loggedUser: UserSession,
  ): Promise<
    BaseResponse<{
      imported: Candidate[];
      failed: {
        candidate: CreateCandidateDto;
        reason: string;
        status: string;
      }[];
    }>
  > {
    const imported: Candidate[] = [];
    const failed: {
      candidate: CreateCandidateDto;
      reason: string;
      status: string;
    }[] = [];
    let job: { id: number; title: string } | null = null;

    if (jobId !== 0) {
      job = await this.prismaService.job.findUnique({
        where: { id: jobId },
        select: { id: true, title: true },
      });

      if (!job) {
        return new BaseResponse({
          data: {
            imported: [],
            failed: candidates.map((candidate) => ({
              candidate,
              reason: 'Invalid jobId: job not found.',
              status: 'error',
            })),
          },
          message: 'Job not found.',
          statusCode: HttpStatus.BAD_REQUEST,
        });
      }
    }

    for (const item of candidates) {
      const { firstName, lastName, title, linkedin, note, ...rest } = item;

      if (!firstName || !linkedin) {
        failed.push({
          candidate: item,
          reason: 'Missing required fields: firstName or linkedin.',
          status: 'error',
        });
        continue;
      }

      const existing = await this.prismaService.candidate.findUnique({
        where: { linkedin },
      });

      if (existing) {
        if (jobId === 0) {
          failed.push({
            candidate: item,
            reason: 'Duplicate linkedin value.',
            status: 'skipped',
          });
          continue;
        }

        const alreadyLinked = await this.prismaService.jobCandidate.findFirst({
          where: {
            candidateId: existing.id,
            jobId: jobId,
          },
        });

        if (alreadyLinked) {
          failed.push({
            candidate: item,
            reason: 'Duplicate linkedin for this job.',
            status: 'skipped',
          });
          continue;
        }

        // candidate exists but not linked to this job → link it
        try {
          await this.prismaService.$transaction(async (tx) => {
            await tx.jobCandidate.create({
              data: {
                candidateId: existing.id,
                jobId,
                createdById: loggedUser.id,
              },
            });

            if (note?.trim()) {
              await tx.candidateNote.create({
                data: {
                  candidateId: existing.id,
                  content: note.trim(),
                  createdById: loggedUser.id,
                },
              });
            }
          });

          imported.push(existing);
          continue;
        } catch (error) {
          failed.push({
            candidate: item,
            reason: error instanceof Error ? error.message : 'Unknown error',
            status: 'error',
          });
          continue;
        }
      }

      try {
        const result = await this.prismaService.$transaction(async (tx) => {
          const created = await tx.candidate.create({
            data: {
              firstName,
              lastName,
              title,
              linkedin,
              ...rest,
              createdById: loggedUser.id,
            },
          });

          if (jobId !== 0) {
            await tx.jobCandidate.create({
              data: {
                candidateId: created.id,
                jobId,
                createdById: loggedUser.id,
              },
            });
          }

          if (note?.trim()) {
            await tx.candidateNote.create({
              data: {
                candidateId: created.id,
                content: note.trim(),
                createdById: loggedUser.id,
              },
            });
          }

          return created;
        });

        imported.push(result);
      } catch (error) {
        failed.push({
          candidate: item,
          reason: error instanceof Error ? error.message : 'Unknown error',
          status: 'error',
        });
      }
    }

    await this.userActivityService.logActivity({
      userId: loggedUser.id,
      action: ActivityAction.CREATE,
      objectType: ActivityObject.JOB_CANDIDATE,
      objectId: 'bulk_import',
      metadata: {
        total: imported.length,
        failed: failed.length,
        jobId: jobId !== 0 ? jobId : null,
      },
    });

    if (job !== null && imported.length > 0) {
      await this.notificationService.broadcast(req, {
        title: 'Candidates imported and assigned to job',
        content: `${imported.length} candidate(s) added to job "${job.title}"`,
        type: NotificationType.CANDIDATE_APPLIED,
        // excludeUserId: loggedUser.id, // TODO, maybe use later
        metadata: {
          jobId: job.id,
          candidateCount: imported.length,
        },
      });
    }

    return new BaseResponse({
      data: { imported, failed },
      message: `Imported ${imported.length} candidates, ${failed.length} failed.`,
      statusCode: HttpStatus.OK,
    });
  }

  async addNote(
    candidateId: number,
    content: string,
    loggedUser: UserSession,
  ): Promise<BaseResponse<any>> {
    try {
      const candidate = await this.prismaService.candidate.findUnique({
        where: { id: candidateId },
      });

      if (!candidate) {
        return new BaseResponse({
          data: null,
          message: `Candidate with ID ${candidateId} not found.`,
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      const note = await this.prismaService.candidateNote.create({
        data: {
          content,
          candidateId,
          createdById: loggedUser.id,
        },
        // TODO, Master API integration
        // include: {
        //   createdBy: true,
        // },
      });

      await this.userActivityService.logActivity({
        userId: loggedUser.id,
        action: 'CREATE',
        objectType: 'CANDIDATE_NOTE',
        objectId: String(note.id),
        metadata: {
          candidateNoteId: note.id,
        },
      });

      return new BaseResponse({
        data: note,
        message: 'Note added successfully.',
        statusCode: HttpStatus.CREATED,
      });
    } catch (error) {
      return new BaseResponse({
        data: null,
        message:
          error instanceof Error ? error.message : 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }
}
